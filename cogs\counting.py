#responsible for handling the counting channel

import discord
from discord.ext import commands
from discord import app_commands
import re
import json
import os
from .db import db

class Counting(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.COUNTING_CHANNEL_ID = 1393125059710947372
        self.CHECK_EMOJI = "<:Check:1379204527273807882>"
        self.X_EMOJI = "<:Unckeck:1378881221274238998>"
        self.CELEBRATION_EMOJI = "<:Wolf_Yay:1349582406415548488>"
        self.EMBED_COLOR = 0x566374
        
        # Initialize data attributes!
        self.current_number = 0
        self.highest_number = 0
        self.last_counter = None
        self.current_streak = 0
        self.current_streak_user = None
        self.counter_stats = {}
        self.failure_stats = {}

    async def load_all_data(self):
        """Load all counting related data from database"""
        try:
            # Get guild ID (assuming single guild bot)
            guild_id = 1393125059115356160  # Replace with your guild ID
            
            # Load counting data
            counting_data = await db.get_counting_data(guild_id)
            self.current_number = counting_data.get('current_number', 0)
            self.highest_number = counting_data.get('highest_number', 0)
            self.last_counter = counting_data.get('last_counter')
            self.current_streak = counting_data.get('current_streak', 0)
            self.current_streak_user = counting_data.get('current_streak_user')
            self.counter_stats = counting_data.get('counter_stats', {})
            self.failure_stats = counting_data.get('failure_stats', {})
            
            print(f"✅ Loaded counting data from database")
        except Exception as e:
            print(f"❌ Error loading counting data from database: {e}")

    async def save_counting_data(self):
        """Save all counting data to database"""
        try:
            guild_id = 1393125059115356160  # Replace with your guild ID
            
            await db.update_counting_data(
                guild_id,
                current_number=self.current_number,
                highest_number=self.highest_number,
                last_counter=self.last_counter,
                current_streak=self.current_streak,
                current_streak_user=self.current_streak_user,
                counter_stats=self.counter_stats,
                failure_stats=self.failure_stats
            )
            print(f"✅ Saved counting data to database")
        except Exception as e:
            print(f"❌ Error saving counting data to database: {e}")

    async def cog_load(self):
        """Called when the cog is loaded"""
        print("🔍 DEBUG: Counting cog loading...")
        
        # Load data from database first
        try:
            await self.load_all_data()
            print(f"🔍 DEBUG: Loaded counting data - Current: {self.current_number}, Highest: {self.highest_number}")
        except Exception as e:
            print(f"❌ ERROR: Failed to load counting data: {e}")
        
        # Announce current number in counting channel
        channel = self.bot.get_channel(self.COUNTING_CHANNEL_ID)
        if channel:
            print(f"🔍 DEBUG: Found counting channel: {channel.name}")
            if self.current_number > 0:  # Only send if there's an active count
                embed = discord.Embed(
                    description=f"Bot restarted. The next number is [ **{self.current_number + 1}** ]",
                    color=self.EMBED_COLOR
                )
                await channel.send(embed=embed)
        else:
            print(f"❌ ERROR: Could not find counting channel with ID {self.COUNTING_CHANNEL_ID}")
        
        print("✅ DEBUG: Counting cog loaded successfully")

    def evaluate_expression(self, expression):
        """Evaluate a basic math expression"""
        try:
            # Remove all spaces from the expression
            expression = expression.replace(" ", "")
            
            # Match basic math expression pattern (now including decimals)
            pattern = r'^\s*(\d*\.?\d+(?:\s*[\+\-x\*\/÷]\s*\d*\.?\d+)*)\s*(?:\s|$)'
            match = re.match(pattern, expression)
            
            if not match:
                return None
            
            # Get the math expression
            math_expr = match.group(1)
            
            # Replace 'x' with '*' for multiplication and '÷' with '/' for division
            math_expr = math_expr.replace('x', '*').replace('÷', '/')
            
            # Additional safety checks
            if any(c not in '0123456789+-*/.e ' for c in math_expr):
                return None
            
            # Evaluate the expression
            result = eval(math_expr, {"__builtins__": {}}, {})
            
            # Check if result is too large
            if abs(result) > 1e10:  # Limit to reasonable numbers
                return None
            
            # Round to handle floating point precision issues
            result = round(result, 2)
            
            # Check if result is effectively a whole number
            if abs(result - round(result)) < 1e-10:
                return int(round(result))
            
            return None  # Return None for non-whole numbers
            
        except:
            return None

    def extract_first_number(self, content):
        """Extract and evaluate the first number or math expression"""
        # First try to evaluate as math expression
        result = self.evaluate_expression(content)
        if result is not None:
            return result
            
        # If not a math expression, try to match a pure number
        match = re.match(r'^\s*(\d+)(?:\s|$)', content)
        if not match:
            return None
            
        try:
            return int(match.group(1))
        except ValueError:
            return None

    @app_commands.command(name="countinghighscore", description="Display the highest counting streak achieved")
    async def counting_highscore(self, interaction: discord.Interaction):
        if interaction.channel_id != self.COUNTING_CHANNEL_ID:
            await interaction.response.send_message("This command can only be used in the counting channel!", ephemeral=True)
            return
        embed = discord.Embed(
            description=f"[ **{self.highest_number}** ] is the current highest number!",
            color=self.EMBED_COLOR
        )
        await interaction.response.send_message(embed=embed)

    @app_commands.command(name="countingleaderboard", description="Show the top users ranked by counting performance")
    async def counting_leaderboard(self, interaction: discord.Interaction):
        embed = discord.Embed(
            title="Counting Leaderboard",
            color=self.EMBED_COLOR
        )

        # Build top counters text
        counters_text = "**Top 5 Counters:**\n"
        top_counters = sorted(self.counter_stats.items(), key=lambda x: x[1], reverse=True)[:5]
        for i, (user_id, count) in enumerate(top_counters, 1):
            user = self.bot.get_user(int(user_id))
            name = user.mention if user else "Unknown User"
            counters_text += f"{i}︲{name}\n    Total Counts: [ {count} ]\n\n" 

        # Build top failures text
        failures_text = "**Top 5 Counting Fails:**\n"
        top_failures = sorted(self.failure_stats.items(), key=lambda x: x[1], reverse=True)[:5]
        for i, (user_id, count) in enumerate(top_failures, 1):
            user = self.bot.get_user(int(user_id))
            name = user.mention if user else "Unknown User"
            failures_text += f"{i}︲{name}\n    Total Fails: [ {count} ]\n\n"

        # Set description with both sections
        embed.description = f"{counters_text}\n{failures_text}"

        await interaction.response.send_message(embed=embed)

    async def award_save(self, user_id: int, channel):
        """Award a save to a user"""
        try:
            user_stats = await db.get_user_counting_stats(user_id)
            new_saves = user_stats.get('saves', 0) + 1
            await db.update_user_counting_stats(user_id, saves=new_saves)
            
            user = self.bot.get_user(user_id)
            embed = discord.Embed(
                description=f"{user.mention} has gained a save! You now have [ **{new_saves}** ] saves!",
                color=self.EMBED_COLOR
            )
            await channel.send(embed=embed)
        except Exception as e:
            print(f"❌ Error awarding save to user {user_id}: {e}")

    async def use_save(self, user_id: int, channel):
        """Use a save for a user"""
        try:
            user_stats = await db.get_user_counting_stats(user_id)
            current_saves = user_stats.get('saves', 0)
            if current_saves <= 0:
                return False
                
            await db.update_user_counting_stats(user_id, saves=current_saves - 1)
            return True
        except Exception as e:
            print(f"❌ Error using save for user {user_id}: {e}")
            return False

    @app_commands.command(name="countingstatistics", description="View your personal counting statistics and performance")
    async def counting_statistics(self, interaction: discord.Interaction):
        if interaction.channel_id != self.COUNTING_CHANNEL_ID:
            await interaction.response.send_message("This command can only be used in the counting channel!", ephemeral=True)
            return
        
        user_id = interaction.user.id
        
        # Get user stats from database
        user_stats = await db.get_user_counting_stats(user_id)
        total_counts = user_stats.get('correct_counts', 0)
        total_fails = user_stats.get('failed_counts', 0)
        saves_count = user_stats.get('saves', 0)
        highest_streak = user_stats.get('highest_streak', 0)
        
        # If current streak is higher than recorded highest and it's the same user, update it
        if self.current_streak_user == interaction.user.id and self.current_streak > highest_streak:
            highest_streak = self.current_streak
            # Update the stored highest streak
            await db.update_user_counting_stats(user_id, highest_streak=highest_streak)
        
        embed = discord.Embed(
            title=f"Counting Statistics | {interaction.user.display_name}",
            color=self.EMBED_COLOR
        )
        
        # Add user's avatar
        avatar_url = interaction.user.avatar.url if interaction.user.avatar else interaction.user.default_avatar.url
        embed.set_thumbnail(url=avatar_url)
        
        embed.add_field(
            name="Highest Streak:",
            value=f"[ **{highest_streak}** ]",
            inline=False
        )
        
        embed.add_field(
            name="Total Counts:",
            value=f"[ **{total_counts}** ]",
            inline=False
        )
        
        embed.add_field(
            name="Total Fails:",
            value=f"[ **{total_fails}** ]",
            inline=False
        )
        
        embed.add_field(
            name="Saves:",
            value=f"[ **{saves_count}** ]",
            inline=False
        )
        
        await interaction.response.send_message(embed=embed)



    async def update_highest_streak(self, user_id: int):
        """Update a user's highest streak if current streak is higher"""
        try:
            user_stats = await db.get_user_counting_stats(user_id)
            if self.current_streak > user_stats.get('highest_streak', 0):
                await db.update_user_counting_stats(user_id, highest_streak=self.current_streak)
        except Exception as e:
            print(f"❌ Error updating highest streak for user {user_id}: {e}")

    async def send_fail_message(self, message, custom_reason=None):
        """Send fail message when counting fails"""
        print(f"[COUNTING] 💥 Counting failed! Resetting from {self.current_number} to 0")
        
        # Update highest streak before resetting
        if self.current_streak_user:
            await self.update_highest_streak(self.current_streak_user)
        
        # Update highest number if current number was higher
        if self.current_number > self.highest_number:
            self.highest_number = self.current_number
            print(f"[COUNTING] 🏆 New high score: {self.highest_number}")
        
        embed = discord.Embed(
            description=f"{message.author.mention} messed up! Start again from [ **1** ]",
            color=self.EMBED_COLOR
        )
        await message.channel.send(embed=embed)
        
        # Reset current number and streak
        self.current_number = 0
        self.last_counter = None
        self.current_streak = 0
        self.current_streak_user = None
        
        # Save the reset state
        await self.save_counting_data()
        print(f"[COUNTING] ✅ Reset complete - ready for number 1")

    @commands.Cog.listener()
    async def on_message(self, message):
        """Handle counting messages - completely rebuilt logic"""
        # Skip if not in counting channel or from bot
        if message.channel.id != self.COUNTING_CHANNEL_ID or message.author.bot:
            return
        
        # Extract number from message
        number = self.extract_first_number(message.content)
        if number is None:
            return  # Not a number, ignore
        
        print(f"[COUNTING] User {message.author.display_name} sent: {number} (Expected: {self.current_number + 1})")
        
        try:
            # Case 1: Correct number
            if number == self.current_number + 1:
                # Check if same user counting twice
                if message.author.id == self.last_counter:
                    await self._handle_same_user_counting(message, number)
                else:
                    await self._handle_correct_number(message, number)
            
            # Case 2: Wrong number
            else:
                await self._handle_wrong_number(message, number)
                
        except Exception as e:
            print(f"[COUNTING ERROR] {e}")
            # Fallback - add X reaction if anything fails
            try:
                await message.add_reaction(self.X_EMOJI)
            except:
                pass

    async def _handle_correct_number(self, message, number):
        """Handle when user sends the correct next number"""
        print(f"[COUNTING] ✅ Correct number from new user!")
        
        # Update counting state
        self.current_number = number
        self.last_counter = message.author.id
        
        # Add checkmark reaction
        await message.add_reaction(self.CHECK_EMOJI)
        
        # Handle streaks
        if self.current_streak_user != message.author.id:
            # Update previous user's highest streak if they had one
            if self.current_streak_user and self.current_streak > 0:
                await self.update_highest_streak(self.current_streak_user)
            
            # Start new streak for this user
            self.current_streak = 1
            self.current_streak_user = message.author.id
        else:
            # Continue existing streak
            self.current_streak += 1
            await self.update_highest_streak(message.author.id)
            
            # Award save every 10 correct counts in a row
            if self.current_streak >= 10:
                await self.award_save(message.author.id, message.channel)
                self.current_streak = 0  # Reset after save
        
        # Celebration for milestones
        if number % 100 == 0:
            await message.add_reaction(self.CELEBRATION_EMOJI)
        
        # Update user stats
        try:
            user_stats = await db.get_user_counting_stats(message.author.id)
            await db.update_user_counting_stats(
                message.author.id, 
                correct_counts=user_stats.get('correct_counts', 0) + 1
            )
        except Exception as e:
            print(f"[COUNTING ERROR] Failed to update user stats: {e}")
        
        # Save all data
        await self.save_counting_data()
        print(f"[COUNTING] State saved - Current: {self.current_number}")

    async def _handle_same_user_counting(self, message, number):
        """Handle when same user tries to count twice in a row"""
        print(f"[COUNTING] ❌ Same user counting twice!")
        
        # Check if user has saves
        user_stats = await db.get_user_counting_stats(message.author.id)
        saves = user_stats.get('saves', 0)
        
        if saves > 0:
            # Use a save
            await db.update_user_counting_stats(message.author.id, saves=saves - 1)
            
            # Allow the count but show it used a save
            self.current_number = number
            self.last_counter = message.author.id
            
            await message.add_reaction(self.X_EMOJI)  # Show it was wrong
            await message.add_reaction(self.CHECK_EMOJI)  # But allowed
            
            embed = discord.Embed(
                description=f"{message.author.mention} used a save! [ **{saves - 1}** ] saves remaining. The next number is [ **{self.current_number + 1}** ]",
                color=self.EMBED_COLOR
            )
            await message.channel.send(embed=embed)
            await self.save_counting_data()
        else:
            # No saves - count fails
            await message.add_reaction(self.X_EMOJI)
            await self.send_fail_message(message)
            
            # Update failure stats
            try:
                user_stats = await db.get_user_counting_stats(message.author.id)
                await db.update_user_counting_stats(
                    message.author.id, 
                    failed_counts=user_stats.get('failed_counts', 0) + 1
                )
            except Exception as e:
                print(f"[COUNTING ERROR] Failed to update failure stats: {e}")

    async def _handle_wrong_number(self, message, number):
        """Handle when user sends wrong number"""
        print(f"[COUNTING] ❌ Wrong number! Got {number}, expected {self.current_number + 1}")
        
        # Check if user has saves
        user_stats = await db.get_user_counting_stats(message.author.id)
        saves = user_stats.get('saves', 0)
        
        if saves > 0:
            # Use a save - don't reset count
            await db.update_user_counting_stats(message.author.id, saves=saves - 1)
            
            await message.add_reaction(self.X_EMOJI)
            embed = discord.Embed(
                description=f"{message.author.mention} used a save! [ **{saves - 1}** ] saves remaining. The next number is still [ **{self.current_number + 1}** ]",
                color=self.EMBED_COLOR
            )
            await message.channel.send(embed=embed)
        else:
            # No saves - count fails and resets
            await message.add_reaction(self.X_EMOJI)
            await self.send_fail_message(message)
            
            # Update failure stats
            try:
                user_stats = await db.get_user_counting_stats(message.author.id)
                await db.update_user_counting_stats(
                    message.author.id, 
                    failed_counts=user_stats.get('failed_counts', 0) + 1
                )
            except Exception as e:
                print(f"[COUNTING ERROR] Failed to update failure stats: {e}")

async def setup(bot):
    print("🔍 DEBUG: Setting up Counting cog...")
    cog = Counting(bot)
    await cog.load_all_data()  # Load data when setting up the cog
    await bot.add_cog(cog)
    print("✅ DEBUG: Counting cog setup completed")





