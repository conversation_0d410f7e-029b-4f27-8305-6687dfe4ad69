# Complete Warning System - Responsible for handling user warnings
# Fixed version with proper database handling and error management

import discord
from discord import app_commands
from discord.ext import commands
import os
from .db import db
from dotenv import load_dotenv
from datetime import datetime
import json
from typing import List, Dict, Any, Optional

class WarningFeature(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.STAFF_CHANNEL_ID = 1393125059886973021
        self.EMBED_COLOR = 0x566374
        
        load_dotenv()
        default_admin_roles = ['1393125059186659361', '1393125059186659362', '1393125059186659363']
        admin_roles_str = os.getenv('ADMIN_ROLES', ','.join(default_admin_roles))
        self.admin_roles = [role.strip() for role in admin_roles_str.split(',') if role.strip()]
        
        # Debug: Print what admin roles were loaded
        print(f"DEBUG: WarningFeature loaded admin roles: {self.admin_roles}")
        print(f"DEBUG: ADMIN_ROLES env var: {os.getenv('ADMIN_ROLES')}")
        print(f"DEBUG: Default admin roles: {default_admin_roles}")

    def has_admin_role(self, member: discord.Member) -> bool:
        """Check if member has admin role"""
        return any(str(role.id) in self.admin_roles for role in member.roles)

    async def add_warning_to_db(self, user_id: int, reason: str, warned_by: int, warned_by_name: str) -> bool:
        """Add a warning to the database with proper error handling"""
        try:
            # Validate inputs
            if not reason or not reason.strip():
                raise ValueError("Warning reason cannot be empty")
            if len(reason) > 1000:
                raise ValueError("Warning reason too long (max 1000 characters)")
            
            # Get current warnings data
            warnings_data = await db.get_warnings_data()
            warnings = warnings_data.get('warnings', {})
            
            # Ensure warnings is a dictionary
            if not isinstance(warnings, dict):
                warnings = {}
            
            # Initialize user warnings if not exists
            user_id_str = str(user_id)
            if user_id_str not in warnings:
                warnings[user_id_str] = []
            
            # Ensure user's warnings is a list
            if not isinstance(warnings[user_id_str], list):
                warnings[user_id_str] = []
            
            # Create warning object
            warning = {
                'reason': str(reason).strip(),
                'warned_by': int(warned_by),
                'warned_by_name': str(warned_by_name),
                'timestamp': datetime.now().isoformat()
            }
            
            # Add warning to user's list
            warnings[user_id_str].append(warning)
            
            # Save to database
            await db.save_warnings_data({'warnings': warnings})
            print(f"SUCCESS: Warning added for user {user_id}: {reason}")
            return True
            
        except Exception as e:
            print(f"ERROR: Failed to add warning for user {user_id}: {e}")
            raise

    async def get_user_warnings(self, user_id: int) -> List[Dict[str, Any]]:
        """Get all warnings for a user with error handling"""
        try:
            warnings_data = await db.get_warnings_data()
            warnings = warnings_data.get('warnings', {})
            
            # Ensure warnings is a dictionary
            if not isinstance(warnings, dict):
                return []
            
            user_warnings = warnings.get(str(user_id), [])
            
            # Ensure user warnings is a list
            if not isinstance(user_warnings, list):
                return []
            
            return user_warnings
            
        except Exception as e:
            print(f"ERROR: Failed to get warnings for user {user_id}: {e}")
            return []

    async def remove_warning_from_db(self, user_id: int, warning_index: int) -> bool:
        """Remove a specific warning from a user"""
        try:
            warnings_data = await db.get_warnings_data()
            warnings = warnings_data.get('warnings', {})
            
            # Ensure warnings is a dictionary
            if not isinstance(warnings, dict):
                return False
            
            user_id_str = str(user_id)
            if user_id_str not in warnings:
                return False
            
            # Ensure user warnings is a list
            if not isinstance(warnings[user_id_str], list):
                return False
            
            # Check if warning index is valid
            if 0 <= warning_index < len(warnings[user_id_str]):
                warnings[user_id_str].pop(warning_index)
                
                # Remove user entry if no warnings left
                if not warnings[user_id_str]:
                    del warnings[user_id_str]
                
                # Save updated warnings
                await db.save_warnings_data({'warnings': warnings})
                print(f"SUCCESS: Warning {warning_index + 1} removed for user {user_id}")
                return True
            
            return False
            
        except Exception as e:
            print(f"ERROR: Failed to remove warning for user {user_id}: {e}")
            return False

    @app_commands.command(name="warn", description="Issues a warning to a user")
    @app_commands.describe(
        member="The member to warn",
        reason="The reason for the warning"
    )
    async def warn(self, interaction: discord.Interaction, member: discord.Member, reason: str):
        """Issue a warning to a user"""
        # Permission check
        if member.id != interaction.user.id and not self.has_admin_role(interaction.user):
            await interaction.response.send_message("❌ You don't have permission to use this command.", ephemeral=True)
            return

        # Basic validation
        if not reason or not reason.strip():
            await interaction.response.send_message("❌ Please provide a reason for the warning.", ephemeral=True)
            return

        if len(reason) > 1000:
            await interaction.response.send_message("❌ Warning reason is too long (max 1000 characters).", ephemeral=True)
            return

        try:
            # Defer the response immediately
            await interaction.response.defer(ephemeral=True)

            # Add warning to database
            await self.add_warning_to_db(member.id, reason, interaction.user.id, interaction.user.display_name)

            # Get current warning count for the user
            user_warnings = await self.get_user_warnings(member.id)
            warning_count = len(user_warnings)

            # Send confirmation to command user
            confirm_embed = discord.Embed(
                description=f"<:Warn:1378881193964998737>︲{member.mention} has been warned!",
                color=self.EMBED_COLOR
            )
            await interaction.followup.send(embed=confirm_embed, ephemeral=True)

            # Send DM to warned user
            try:
                dm_embed = discord.Embed(
                    title="<:Warn:1378881193964998737>︲You Have Been Warned!",
                    description=f"Reason:\n{reason}\n\nA total of [ 3 ] warnings may result in punishment!",
                    color=self.EMBED_COLOR
                )
                await member.send(embed=dm_embed)
            except discord.Forbidden:
                await interaction.followup.send("⚠️ Could not send DM to user (DMs disabled).", ephemeral=True)
            except Exception as dm_error:
                print(f"Error sending DM: {dm_error}")

            # Send log to staff channel
            try:
                staff_channel = self.bot.get_channel(self.STAFF_CHANNEL_ID)
                if staff_channel:
                    log_embed = discord.Embed(
                        title=f"<:Warn:1378881193964998737>︲Warned︲{member.display_name}",
                        description=f"{member.mention}",
                        color=self.EMBED_COLOR
                    )

                    log_embed.add_field(
                        name="Reason:",
                        value=reason,
                        inline=False
                    )

                    log_embed.add_field(
                        name="Warned By:",
                        value=interaction.user.mention,
                        inline=True
                    )

                    log_embed.set_thumbnail(url=member.display_avatar.url)

                    await staff_channel.send(embed=log_embed)
            except Exception as log_error:
                print(f"Error sending staff log: {log_error}")

        except ValueError as ve:
            # Handle validation errors
            error_msg = str(ve)
            await interaction.followup.send(f"❌ {error_msg}", ephemeral=True)
        except Exception as e:
            print(f"ERROR: Error in warn command: {e}")
            error_msg = "An unexpected error occurred while processing the warning."
            if not interaction.response.is_done():
                await interaction.response.send_message(f"❌ {error_msg}", ephemeral=True)
            else:
                await interaction.followup.send(f"❌ {error_msg}", ephemeral=True)

    @app_commands.command(name="userwarnings", description="Displays all warnings issued to a user")
    @app_commands.describe(member="The member whose warnings you want to view")
    async def userwarnings(self, interaction: discord.Interaction, member: discord.Member):
        """Display all warnings for a user"""
        # Check if user has admin role
        if not self.has_admin_role(interaction.user):
            await interaction.response.send_message("❌ You don't have permission to use this command.", ephemeral=True)
            return

        try:
            # Defer the response since we're doing database operations
            await interaction.response.defer()

            user_warnings = await self.get_user_warnings(member.id)

            if not user_warnings or len(user_warnings) == 0:
                no_warnings_embed = discord.Embed(
                    description=f"✅ {member.mention} has no warnings!",
                    color=0x00ff00  # Green color for no warnings
                )
                await interaction.followup.send(embed=no_warnings_embed)
                return

            # Build the description with all warnings
            description = f"Total Warnings: [ {len(user_warnings)} ]\n\n"
            
            for i, warning in enumerate(user_warnings, 1):
                try:
                    # Handle potential missing fields
                    reason = warning.get('reason', 'No reason provided')
                    warned_by_id = warning.get('warned_by', None)
                    timestamp = warning.get('timestamp', '')
                    
                    # Get the user mention for warned_by
                    if warned_by_id:
                        warned_by_mention = f"<@{warned_by_id}>"
                    else:
                        warned_by_mention = warning.get('warned_by_name', 'Unknown')
                    
                    if timestamp:
                        try:
                            warning_time = datetime.fromisoformat(timestamp)
                            formatted_time = warning_time.strftime("%m/%d/%Y %I:%M %p")
                        except (ValueError, TypeError):
                            formatted_time = "Unknown date"
                    else:
                        formatted_time = "Unknown date"

                    description += f"Warning ID #{i}\n**Reason:**\n{reason}\n**Warned By:**\n{warned_by_mention}\n**Date:**\n{formatted_time}\n\n"
                    
                except Exception as field_error:
                    print(f"Error processing warning {i}: {field_error}")
                    description += f"Warning ID #{i}\n**Reason:**\nError loading warning data\n**Warned By:**\nUnknown\n**Date:**\nUnknown\n\n"

            embed = discord.Embed(
                title=f"<:Warn:1378881193964998737>︲User Warnings︲{member.display_name}",
                description=description.strip(),
                color=self.EMBED_COLOR
            )
            
            embed.set_thumbnail(url=member.display_avatar.url)

            await interaction.followup.send(embed=embed)

        except Exception as e:
            print(f"ERROR: Error in userwarnings command: {e}")
            error_msg = "An error occurred while retrieving warnings."
            if not interaction.response.is_done():
                await interaction.response.send_message(f"❌ {error_msg}")
            else:
                await interaction.followup.send(f"❌ {error_msg}")

    @app_commands.command(name="warnremove", description="Removes a warning from a member")
    @app_commands.describe(
        member="The member to remove a warning from",
        warning_number="The warning number to remove (1, 2, 3, etc.)"
    )
    async def warnremove(self, interaction: discord.Interaction, member: discord.Member, warning_number: int):
        """Remove a specific warning from a user"""
        if not self.has_admin_role(interaction.user):
            await interaction.response.send_message("❌ You don't have permission to use this command.", ephemeral=True)
            return

        try:
            # Defer the response since we're doing database operations
            await interaction.response.defer(ephemeral=True)

            user_warnings = await self.get_user_warnings(member.id)

            if not user_warnings or len(user_warnings) == 0:
                no_warnings_embed = discord.Embed(
                    description=f"❌ {member.mention} has no warnings to remove.",
                    color=self.EMBED_COLOR
                )
                await interaction.followup.send(embed=no_warnings_embed, ephemeral=True)
                return

            if warning_number < 1 or warning_number > len(user_warnings):
                await interaction.followup.send(f"❌ Invalid warning number! Please choose between 1 and {len(user_warnings)}.", ephemeral=True)
                return

            # Get the warning to be removed for logging
            try:
                removed_warning = user_warnings[warning_number - 1]
                removed_reason = removed_warning.get('reason', 'No reason provided')
                removed_by_name = removed_warning.get('warned_by_name', 'Unknown')
            except (IndexError, TypeError):
                removed_reason = 'Unknown warning'
                removed_by_name = 'Unknown'
            
            # Remove the warning from database
            success = await self.remove_warning_from_db(member.id, warning_number - 1)
            
            if not success:
                await interaction.followup.send("❌ Failed to remove warning from database.", ephemeral=True)
                return

            # Get updated warning count
            updated_warnings = await self.get_user_warnings(member.id)
            remaining_count = len(updated_warnings)

            # Send confirmation embed to the command user
            embed = discord.Embed(
                description=f"<:Check:1379204527273807882>︲Warning [ {warning_number} ] removed from {member.mention}",
                color=self.EMBED_COLOR
            )
            await interaction.followup.send(embed=embed, ephemeral=True)

            # Send log to staff channel
            try:
                staff_channel = self.bot.get_channel(self.STAFF_CHANNEL_ID)
                if staff_channel:
                    log_embed = discord.Embed(
                        title=f"<:Check:1379204527273807882>︲Warning Removed︲{member.display_name}",
                        description=f"{member.mention}",
                        color=self.EMBED_COLOR
                    )
                    log_embed.add_field(
                        name="Removed Warning:",
                        value=removed_reason,
                        inline=False
                    )
                    log_embed.add_field(
                        name="Removed By:",
                        value=interaction.user.mention,
                        inline=True
                    )
                    log_embed.set_thumbnail(url=member.display_avatar.url)
                    await staff_channel.send(embed=log_embed)
            except Exception as log_error:
                print(f"Error sending staff log: {log_error}")

        except Exception as e:
            print(f"ERROR: Error in warnremove command: {e}")
            error_msg = "An error occurred while removing the warning."
            if not interaction.response.is_done():
                await interaction.response.send_message(f"❌ {error_msg}", ephemeral=True)
            else:
                await interaction.followup.send(f"❌ {error_msg}", ephemeral=True)

    @app_commands.command(name="clearwarnings", description="Removes all warnings from a member")
    @app_commands.describe(member="The member to clear all warnings from")
    async def clearwarnings(self, interaction: discord.Interaction, member: discord.Member):
        """Clear all warnings from a user"""
        if not self.has_admin_role(interaction.user):
            await interaction.response.send_message("❌ You don't have permission to use this command.", ephemeral=True)
            return

        try:
            # Defer the response since we're doing database operations
            await interaction.response.defer(ephemeral=True)

            user_warnings = await self.get_user_warnings(member.id)

            if not user_warnings or len(user_warnings) == 0:
                no_warnings_embed = discord.Embed(
                    description=f"❌ {member.mention} has no warnings to clear.",
                    color=self.EMBED_COLOR
                )
                await interaction.followup.send(embed=no_warnings_embed, ephemeral=True)
                return

            warning_count = len(user_warnings)

            # Clear all warnings
            warnings_data = await db.get_warnings_data()
            warnings = warnings_data.get('warnings', {})
            
            user_id_str = str(member.id)
            if user_id_str in warnings:
                del warnings[user_id_str]
                await db.save_warnings_data({'warnings': warnings})

            # Send confirmation embed
            embed = discord.Embed(
                description=f"<:Check:1379204527273807882>︲All warnings removed from {member.mention}",
                color=self.EMBED_COLOR
            )
            await interaction.followup.send(embed=embed, ephemeral=True)

            # Send log to staff channel
            try:
                staff_channel = self.bot.get_channel(self.STAFF_CHANNEL_ID)
                if staff_channel:
                    log_embed = discord.Embed(
                        title=f"<:Check:1349166442804940943>︲All Warnings Cleared︲{member.display_name}",
                        description=f"{member.mention}",
                        color=0x00ff00
                    )
                    log_embed.add_field(
                        name="Warnings Cleared:",
                        value=f"{warning_count} warnings",
                        inline=True
                    )
                    log_embed.add_field(
                        name="Cleared By:",
                        value=interaction.user.mention,
                        inline=True
                    )
                    log_embed.set_thumbnail(url=member.display_avatar.url)
                    await staff_channel.send(embed=log_embed)
            except Exception as log_error:
                print(f"Error sending staff log: {log_error}")

        except Exception as e:
            print(f"ERROR: Error in clearwarnings command: {e}")
            error_msg = "An error occurred while clearing warnings."
            if not interaction.response.is_done():
                await interaction.response.send_message(f"❌ {error_msg}", ephemeral=True)
            else:
                await interaction.followup.send(f"❌ {error_msg}", ephemeral=True)

async def setup(bot):
    await bot.add_cog(WarningFeature(bot))