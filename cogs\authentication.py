# Responsible for handling user authentication and verification

import discord
from discord.ext import commands
from discord.ui import <PERSON><PERSON>, View
import json
import os
import asyncio
from .db import db

class Authentication(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        
        # Configuration
        self.STAFF_CHANNEL_ID = 1393125059886973019
        self.EMBED_COLOR = 0x566374
        self.CHECK_EMOJI = "<:Check:1379204527273807882>"
        
        # Database will handle data persistence
        
        # Authentication questions
        self.questions = [
            "Have you read the regulations in the #server-regulations channel, and do you agree to follow them?",  
            "What name or nickname would you like us to call you, and how old are you?",
            "If you are a furry, how long have you been interested in it? What first drew you to it? Describe to us your fursona if you have one!",
            "If you are a Christian, what does being a Christian mean to you personally? How has God helped to shape your life?",
            "What denomination do you belong to, and what does it mean to you personally?",
            "What are some of your favorite activities or hobbies that you enjoy doing in your spare time?",
            "Why do you wish to join the Faithful Fur Fellowship server?",
            "How did you hear about our server, or who referred you to it?"
        ]
        
        # State tracking
        self.user_answers = {}  # Temporary storage for form answers
        self.active_forms = set()
        self.last_submission_time = {}
        self.user_forms = {}  # Track message IDs for staff verification

    async def load_user_forms(self):
        """Load user forms (message tracking) from database"""
        try:
            data = await db.get_user_form("_message_tracking")
            self.user_forms = data if data else {}
            print(f"✅ Loaded {len(self.user_forms)} user form message trackings from database")
        except Exception as e:
            print(f"❌ Error loading user forms from database: {e}")
            self.user_forms = {}

    async def save_user_forms(self):
        """Save user forms (message tracking) to database"""
        try:
            await db.save_user_form("_message_tracking", self.user_forms)
            print(f"✅ Saved user form message tracking to database")
        except Exception as e:
            print(f"❌ Error saving user forms to database: {e}")

    async def cleanup_user_data(self, user_id):
        """Clean up user data from all tracking dictionaries"""
        if str(user_id) in self.user_forms:
            del self.user_forms[str(user_id)]
            await self.save_user_forms()
        if user_id in self.user_answers:
            del self.user_answers[user_id]
        if user_id in self.active_forms:
            self.active_forms.remove(user_id)
        if user_id in self.last_submission_time:
            del self.last_submission_time[user_id]

    @commands.Cog.listener()
    async def on_member_join(self, member):
        """Send welcome message when new member joins"""
        await self.cleanup_user_data(member.id)
        
        embed = discord.Embed(
            title="Welcome to Faithful Fur Fellowship!",
            description=f"Click the [ {self.CHECK_EMOJI} ] emoji to begin the authentication form!",
            color=self.EMBED_COLOR
        )
        welcome_message = await member.send(embed=embed)
        await welcome_message.add_reaction(self.CHECK_EMOJI)

    @commands.Cog.listener()
    async def on_reaction_add(self, reaction, user):
        """Handle reaction to start authentication form"""
        if not isinstance(reaction.message.channel, discord.DMChannel) or str(reaction.emoji) != self.CHECK_EMOJI or user.bot:
            return
            
        if user.id in self.active_forms:
            await user.send("You are already filling out a form! Please complete it or wait for the timeout.")
            return
        
        current_time = discord.utils.utcnow()
        if user.id in self.last_submission_time:
            time_diff = (current_time - self.last_submission_time[user.id]).total_seconds()
            if time_diff < 300:  # 5 minute cooldown
                remaining_time = int(300 - time_diff)
                minutes = remaining_time // 60
                seconds = remaining_time % 60
                await user.send(f"Please wait {minutes} minutes and {seconds} seconds before submitting another application.")
                return
        
        await self.start_form(user)

    async def start_form(self, user):
        """Initialize and start the authentication form"""
        self.active_forms.add(user.id)
        self.user_answers[user.id] = []
        await self.ask_question(user, 0)

    async def ask_question(self, user, question_index):
        """Ask questions and handle responses"""
        if question_index >= len(self.questions):
            thank_you_embed = discord.Embed(
                title="Application Submitted!",
                description="Thank you! Your application will be reviewed within the next 72 hours!",
                color=self.EMBED_COLOR
            )
            await user.send(embed=thank_you_embed)
            await self.send_form_to_staff(user)
            self.active_forms.remove(user.id)
            return

        embed = discord.Embed(
            title=f"Authentication Question [ {question_index + 1} / {len(self.questions)} ]",
            description=self.questions[question_index],
            color=self.EMBED_COLOR
        )
        await user.send(embed=embed)

        try:
            response = await self.bot.wait_for(
                "message",
                check=lambda m: m.author == user and m.channel == user.dm_channel,
                timeout=300
            )
            self.user_answers[user.id].append(response.content)
            await self.ask_question(user, question_index + 1)
        except asyncio.TimeoutError:
            await user.send(f"You took too long to respond. Please start the form again by clicking the [ {self.CHECK_EMOJI} ] emoji.")
            self.active_forms.remove(user.id)

    async def send_form_to_staff(self, user):
        """Send completed form to staff channel"""
        staff_channel = self.bot.get_channel(self.STAFF_CHANNEL_ID)
        if not staff_channel:
            print("Error: Staff channel not found!")
            return

        # Add a small delay to ensure Discord has processed the user properly
        await asyncio.sleep(2)

        self.last_submission_time[user.id] = discord.utils.utcnow()

        # Try to get fresh user data from Discord
        try:
            fresh_user = await self.bot.fetch_user(user.id)
            user = fresh_user
        except Exception as e:
            print(f"⚠️ Could not fetch fresh user data: {e}")

        # Try to get the member object for more reliable data
        member = None
        for guild in self.bot.guilds:
            member = guild.get_member(user.id)
            if member:
                break

        # Use member if available, otherwise fall back to user
        display_user = member if member else user

        # Debug logging
        print(f"🔍 Verification form - User ID: {user.id}")
        print(f"🔍 Verification form - Member found: {member is not None}")
        print(f"🔍 Verification form - Display name: {display_user.display_name}")
        print(f"🔍 Verification form - Profile URL: https://discord.com/users/{user.id}")

        # Calculate account age
        now = discord.utils.utcnow()
        age_delta = now - user.created_at
        years = age_delta.days // 365
        remaining_days = age_delta.days % 365
        months = remaining_days // 30
        final_days = remaining_days % 30
        age_str = f"{years} years, {months} months, {final_days} days"

        embed = discord.Embed(
            title=f"Authentication Form | {display_user.display_name}",
            color=self.EMBED_COLOR
        )
        embed.description = f"{display_user.mention}"
        embed.set_thumbnail(url=display_user.display_avatar.url)
        
        for i, question in enumerate(self.questions):
            embed.add_field(
                name=f"**{question}**",
                value=f"{self.user_answers[user.id][i]}",
                inline=False
            )
        
        embed.set_footer(text=f"Account Age: {age_str} | Status: Pending")

        view = self.create_staff_view()
        message = await staff_channel.send(embed=embed, view=view)
        self.user_forms[str(user.id)] = {"message_id": message.id}
        await self.save_user_forms()

    def create_staff_view(self):
        """Create button view for staff actions"""
        view = View(timeout=None)  # Set timeout to None for persistence
        view.add_item(Button(
            label="︲Accept", 
            style=discord.ButtonStyle.gray, 
            custom_id="verify",
            emoji="<:Check:1379204527273807882>"
        ))
        view.add_item(Button(
            label="︲Question", 
            style=discord.ButtonStyle.gray, 
            custom_id="question",
            emoji="<:Vote:1378881222628999270>"
        ))
        view.add_item(Button(
            label="︲Reject", 
            style=discord.ButtonStyle.gray, 
            custom_id="deny",
            emoji="<:Unckeck:1378881221274238998>"
        ))
        return view

    def create_disabled_view(self):
        """Create disabled button view"""
        view = View()
        view.add_item(Button(
            label="︲Accept", 
            style=discord.ButtonStyle.gray, 
            custom_id="verify",
            emoji="<:Check:1379204527273807882>",
            disabled=True
        ))
        view.add_item(Button(
            label="︲Question", 
            style=discord.ButtonStyle.gray, 
            custom_id="question",
            emoji="<:Vote:1378881222628999270>",
            disabled=True
        ))
        view.add_item(Button(
            label="︲Reject", 
            style=discord.ButtonStyle.gray, 
            custom_id="deny",
            emoji="<:Unckeck:1378881221274238998>",
            disabled=True
        ))
        return view

    @commands.Cog.listener()
    async def on_interaction(self, interaction: discord.Interaction):
        """Handle button interactions"""
        if interaction.type != discord.InteractionType.component:
            return

        # Only handle interactions for verify/deny/question buttons
        if interaction.data["custom_id"] not in ["verify", "deny", "question"]:
            return

        try:
            user_id = next((key for key, value in self.user_forms.items() 
                          if value["message_id"] == interaction.message.id), None)
            if not user_id:
                await interaction.response.send_message("Could not find the application data.", ephemeral=True)
                return

            user = await self.bot.fetch_user(int(user_id))
            member = interaction.guild.get_member(int(user_id))
            disabled_view = self.create_disabled_view()

            if interaction.data["custom_id"] == "verify":
                await self.handle_verify(interaction, member, disabled_view, user_id)
            elif interaction.data["custom_id"] == "deny":
                await self.handle_deny(interaction, member, disabled_view, user_id)
            elif interaction.data["custom_id"] == "question":
                await self.handle_question(interaction, user)

        except Exception as e:
            await interaction.response.send_message(f"An error occurred: {str(e)}", ephemeral=True)

    async def handle_verify(self, interaction, member, disabled_view, user_id):
        """Handle verify button interaction"""
        if not member:
            await interaction.response.send_message("User is no longer in the server.", ephemeral=True)
            return

        role = discord.utils.get(interaction.guild.roles, id=1393125059161358463)  # Members role ID
        if not role:
            await interaction.response.send_message("The 'Members' role could not be found.", ephemeral=True)
            return

        try:
            await member.add_roles(role)
            embed = interaction.message.embeds[0]
            footer_text = embed.footer.text.rsplit("|", 1)[0] + "| Status: Verified"
            embed.set_footer(text=footer_text)
            await interaction.message.edit(embed=embed, view=disabled_view)
            await interaction.response.defer()
            await self.cleanup_user_data(user_id)
        except discord.Forbidden:
            await interaction.response.send_message("Error: Missing permissions to add roles.", ephemeral=True)
        except Exception as e:
            await interaction.response.send_message(f"An error occurred: {str(e)}", ephemeral=True)

    async def handle_deny(self, interaction, member, disabled_view, user_id):
        """Handle deny button interaction"""
        if member:
            await member.kick(reason="Application denied")
            embed = interaction.message.embeds[0]
            footer_text = embed.footer.text.rsplit("|", 1)[0] + "| Status: Denied"
            embed.set_footer(text=footer_text)
            await interaction.message.edit(embed=embed, view=disabled_view)
            await interaction.response.defer()
            await self.cleanup_user_data(user_id)

    async def handle_question(self, interaction: discord.Interaction, user):
        """Handle question button interaction"""
        embed = discord.Embed(
            title="What would you like to ask the user?",
            description="",
            color=self.EMBED_COLOR
        )
        await interaction.response.send_message(embed=embed)

        try:
            # Wait for the staff member's question
            question_msg = await self.bot.wait_for(
                "message",
                check=lambda m: m.author == interaction.user and m.channel == interaction.channel,
                timeout=300
            )

            # Send the question to the user
            user_embed = discord.Embed(
                title="A Message From Faithful Fur Fellowship:",
                description=question_msg.content,
                color=self.EMBED_COLOR
            )
            await user.send(embed=user_embed)

            # Confirm to staff that the question was sent
            confirm_embed = discord.Embed(
                title="Your question has been sent to the user!",
                description="",
                color=self.EMBED_COLOR
            )
            await interaction.followup.send(embed=confirm_embed)

            # Wait for user's response
            try:
                response_msg = await self.bot.wait_for(
                    "message",
                    check=lambda m: isinstance(m.channel, discord.DMChannel) and m.author == user,
                    timeout=600  # 10 minute timeout
                )

                # Send confirmation to user
                confirmation_embed = discord.Embed(
                    title="Message Sent To Faithful Fur Fellowship!",
                    color=self.EMBED_COLOR
                )
                await user.send(embed=confirmation_embed)

                # Send the response back to the staff channel
                response_embed = discord.Embed(
                    title=f"{user.display_name} Has Responded:",
                    description=f"{response_msg.content}",
                    color=self.EMBED_COLOR
                )
                await interaction.channel.send(embed=response_embed)

            except asyncio.TimeoutError:
                timeout_embed = discord.Embed(
                    title="Response Timeout",
                    description=f"{user.mention} did not respond within 10 minutes.",
                    color=discord.Color.red()
                )
                await interaction.channel.send(embed=timeout_embed)

        except asyncio.TimeoutError:
            await interaction.followup.send("No question was provided within the time limit.", ephemeral=True)
        except discord.Forbidden:
            await interaction.followup.send("Unable to send message to the user. They may have DMs disabled.", ephemeral=True)
        except Exception as e:
            await interaction.followup.send(f"An error occurred: {str(e)}", ephemeral=True)



    @commands.Cog.listener()
    async def on_member_remove(self, member):
        """Handle member leaving the server"""
        member_id = str(member.id)
        staff_channel = self.bot.get_channel(self.STAFF_CHANNEL_ID)
        
        try:
            async for message in staff_channel.history(limit=200):
                if message.embeds and message.embeds[0].description:
                    embed = message.embeds[0]
                    if f"https://discord.com/users/{member_id}" in embed.description:
                        disabled_view = self.create_disabled_view()
                        footer_text = embed.footer.text.rsplit("|", 1)[0] + "| Status: Left Server"
                        embed.set_footer(text=footer_text)
                        await message.edit(embed=embed, view=disabled_view)
                        await self.cleanup_user_data(member_id)
                        break
        except Exception as e:
            print(f"Error in on_member_remove: {e}")

    async def cog_load(self):
        """Setup persistent views when the cog loads"""
        await self.load_user_forms()
        await self.restore_authentication_views()

    async def restore_authentication_views(self):
        """Restore button functionality for existing authentication forms"""
        staff_channel = self.bot.get_channel(self.STAFF_CHANNEL_ID)
        if not staff_channel:
            print("Staff channel not available during Authentication cog load")
            return

        for user_id, data in self.user_forms.items():
            try:
                message = await staff_channel.fetch_message(data["message_id"])
                if message:
                    # Add persistent view to the bot
                    view = self.create_staff_view()
                    self.bot.add_view(view)
            except discord.NotFound:
                # Message no longer exists, clean up the entry
                print(f"Cleaning up missing authentication message for user {user_id}")
                del self.user_forms[user_id]
                await self.save_user_forms()
            except Exception as e:
                print(f"Error restoring authentication view for user {user_id}: {e}")

async def setup(bot):
    await bot.add_cog(Authentication(bot))



