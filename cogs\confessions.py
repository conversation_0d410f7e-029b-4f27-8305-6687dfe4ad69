import discord
from discord import app_commands
from discord.ext import commands
from discord.ui import Button, View  # Remove ButtonStyle from here
import os
import json
from datetime import datetime
import random
import asyncio
from .db import db

class ConfessionButtons(View):
    def __init__(self, bot, user_id, confession, confession_id, message_id=None):
        super().__init__(timeout=None)
        self.bot = bot
        self.user_id = user_id
        self.confession = confession
        self.confession_id = str(confession_id)  # Ensure it's a string
        self.message_id = message_id
        
        # Set custom_id for each button to include confession_id
        for item in self.children:
            if isinstance(item, Button):
                item.custom_id = f"{item.custom_id}_{self.confession_id}"

    @discord.ui.button(
        label="︲Accept", 
        style=discord.ButtonStyle.gray, 
        custom_id="accept",
        emoji="<:Check:1379204527273807882>"
    )
    async def accept_button(self, interaction: discord.Interaction, button: Button):
        await self.bot.get_cog('Confessions').handle_confession_accept(
            interaction, 
            self.user_id, 
            self.confession_id
        )

    @discord.ui.button(
        label="︲Reject", 
        style=discord.ButtonStyle.gray, 
        custom_id="reject",
        emoji="<:Unckeck:1378881221274238998>"
    )
    async def reject_button(self, interaction: discord.Interaction, button: Button):
        await self.bot.get_cog('Confessions').handle_confession_reject(
            interaction, 
            self.user_id, 
            self.confession_id
        )

    @discord.ui.button(
        label="︲Vote", 
        style=discord.ButtonStyle.gray, 
        custom_id="vote",
        emoji="<:Vote:1378881222628999270>"
    )
    async def vote_button(self, interaction: discord.Interaction, button: Button):
        await self.bot.get_cog('Confessions').vote_button_callback(interaction)

class VoteView(View):
    def __init__(self, bot, target_user_id, confession_id, yes_votes=None, no_votes=None):
        super().__init__(timeout=None)  # Set timeout to None for persistence!
        self.bot = bot
        self.target_user_id = target_user_id
        self.confession_id = confession_id
        self.yes_votes = set(yes_votes) if yes_votes is not None else set()
        self.no_votes = set(no_votes) if no_votes is not None else set()
        self.message = None
        self.timeout_task = None
        
        # Update initial button labels!
        yes_button = [x for x in self.children if x.custom_id == "vote_yes"][0]
        no_button = [x for x in self.children if x.custom_id == "vote_no"][0]
        yes_button.label = f"︲Yes︲[ {len(self.yes_votes)} ]"
        no_button.label = f"︲No︲[ {len(self.no_votes)} ]"
    
    async def save_vote_data(self):
        """Save current vote data through the confessions cog"""
        try:
            confessions_cog = self.bot.get_cog('Confessions')
            if confessions_cog:
                # Update the active votes in the cog!
                confessions_cog.active_votes[self.confession_id] = {
                    "user_id": self.target_user_id,
                    "yes_votes": list(self.yes_votes),
                    "no_votes": list(self.no_votes),
                    "message_id": self.message.id if self.message else None
                }
                # Save to database
                await confessions_cog.save_vote_data()
        except Exception as e:
            print(f"❌ Error saving vote data from VoteView: {e}")

    async def start_timeout(self):
        """Start the 24-hour timeout timer"""
        await asyncio.sleep(86400)  # 24 hours = 86400
        await self.handle_timeout()

    async def handle_timeout(self):
        """Handle what happens when the vote times out"""
        # Disable all buttons
        for child in self.children:
            child.disabled = True

        # Create the timeout embed!
        # Check if this is a temp ID (pending) or final ID
        display_id = "PENDING" if str(self.confession_id).startswith("temp_") else f"#{self.confession_id}"
        timeout_embed = discord.Embed(
            description=f"Confession [ **{display_id}** ] was canceled!, it has been 24 hours!",
            color=0x566374
        )

        try:
            if self.message:
                channel = self.message.channel
                # Send the timeout message
                await channel.send(embed=timeout_embed)
                # Update the original message with disabled buttons
                await self.message.edit(view=self)
        except Exception as e:
            print(f"Error in vote timeout handling: {e}")

    @discord.ui.button(
        label="︲Yes︲[ 0 ]", 
        style=discord.ButtonStyle.gray, 
        custom_id="vote_yes",
        emoji="<:Check:1349132518070157374>"
    )
    async def yes_button(self, interaction: discord.Interaction, button: Button):
        user_id = interaction.user.id
        
        # If user already voted yes, ignore
        if user_id in self.yes_votes:
            await interaction.response.send_message("You've already voted yes!", ephemeral=True)
            return

        # Remove user's previous vote if they had one
        if user_id in self.no_votes:
            self.no_votes.remove(user_id)
            no_button = [x for x in self.children if x.custom_id == "vote_no"][0]
            no_button.label = f"︲No︲[ {len(self.no_votes)} ]"

        # Add new yes vote
        self.yes_votes.add(user_id)
        button.label = f"︲Yes︲[ {len(self.yes_votes)} ]"
        
        # Save vote data
        await self.save_vote_data()
        
        if len(self.yes_votes) >= 3:
            user = await self.bot.fetch_user(self.target_user_id)
            # Check if this is a temp ID (pending) or final ID
            display_id = "PENDING" if str(self.confession_id).startswith("temp_") else f"#{self.confession_id}"
            reveal_embed = discord.Embed(
                description=f"The username of confession [ **{display_id}** ] is {user.mention}",
                color=0x566374
            )

            # Disable all vote buttons (but NOT the confession Accept/Reject buttons)
            for child in self.children:
                child.disabled = True

            # Send reveal message and update original message with disabled buttons
            await interaction.response.edit_message(view=self)
            await interaction.followup.send(embed=reveal_embed)

            # Add a note that the confession still needs to be accepted/rejected
            note_embed = discord.Embed(
                description="⚠️ **Note:** The confession still needs to be **Accepted** or **Rejected** using the buttons on the original confession message.",
                color=0xFFAA00
            )
            await interaction.followup.send(embed=note_embed, ephemeral=True)
        else:
            await interaction.response.edit_message(view=self)
       

    @discord.ui.button(
        label="︲No︲[ 0 ]", 
        style=discord.ButtonStyle.gray, 
        custom_id="vote_no",
        emoji="<:XMark:1349132522600005756>"
    )
    async def no_button(self, interaction: discord.Interaction, button: Button):
        user_id = interaction.user.id

        # If user already voted no, ignore
        if user_id in self.no_votes:
            await interaction.response.send_message("You've already voted no!", ephemeral=True)
            return

        # Remove user's previous vote if they had one
        if user_id in self.yes_votes:
            self.yes_votes.remove(user_id)
            yes_button = [x for x in self.children if x.custom_id == "vote_yes"][0]
            yes_button.label = f"︲Yes︲[ {len(self.yes_votes)} ]"

        # Add new no vote
        self.no_votes.add(user_id)
        button.label = f"︲No︲[ {len(self.no_votes)} ]"
        
        # Save vote data
        await self.save_vote_data()
        
        await interaction.response.edit_message(view=self)

class Confessions(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.CONFESSION_CHANNEL_ID = 1393125059886973012
        self.STAFF_CHANNEL_ID = 1393125059886973020
        self.EMBED_COLOR = 0x566374
        self.debug_mode = False  # Add this line
        
        # Define status emojis
        self.PENDING_EMOJI = "<:Wait:1378881216580681759>"
        self.ACCEPTED_EMOJI = "<:Check:1379204527273807882>"
        self.REJECTED_EMOJI = "<:Unckeck:1378881221274238998>"
        
        # Initialize data attributes
        self.active_votes = {}  # Track active vote views
        self.confessions = {}  # Initialize confessions data

    async def cog_load(self):
        """Load confession data from database when cog loads"""
        # Load confessions data from database
        self.confessions = await db.get_confessions_data()
        
        # Ensure we have the correct last_id from the database
        print(f"🔍 Loaded last confession ID from database: {self.confessions.get('last_id', 0)}")
        
        # Make sure we have all required keys in the confessions dictionary
        if 'last_id' not in self.confessions:
            self.confessions['last_id'] = 0
        if 'temp_id_counter' not in self.confessions:
            self.confessions['temp_id_counter'] = 0
        if 'active_confessions' not in self.confessions:
            self.confessions['active_confessions'] = {}
        if 'message_ids' not in self.confessions:
            self.confessions['message_ids'] = {}
        if 'current_id' not in self.confessions:
            self.confessions['current_id'] = self.confessions.get('last_id', 0)
        if 'active_votes' not in self.confessions:
            self.confessions['active_votes'] = {}
        
        # Load active votes from database
        if 'active_votes' in self.confessions:
            self.active_votes = self.confessions['active_votes']
        
        # Force save to ensure database has the correct values
        await self.save_confessions()
        
        # Setup persistent views
        await self.setup_persistent_views()

    async def setup_persistent_views(self):
        """Setup persistent views for active confessions and votes"""
        try:
            # Get active confessions from database
            active_confessions = await db.get_active_confessions()
            
            # Setup confession buttons for each active confession
            for confession in active_confessions:
                conf_id = str(confession['id'])
                message_id = confession.get('staff_message_id')
                
                if message_id:
                    view = ConfessionButtons(
                        self.bot,
                        confession["user_id"],
                        confession["content"],
                        conf_id,
                        message_id=message_id
                    )
                    self.bot.add_view(view)
        except Exception as e:
            print(f"❌ Error setting up persistent views: {e}")

        # Setup vote views
        for conf_id, vote_data in self.active_votes.items():
            vote_view = VoteView(
                self.bot,
                vote_data["user_id"],
                conf_id,
                yes_votes=vote_data.get("yes_votes", []),
                no_votes=vote_data.get("no_votes", [])
            )
            self.bot.add_view(vote_view)

    async def restore_active_confessions(self):
        """Restore buttons for active confessions after bot restart"""
        staff_channel = self.bot.get_channel(self.STAFF_CHANNEL_ID)
        if not staff_channel:
            # Only print in debug mode or remove entirely since it's expected during startup
            if self.debug_mode:  # Add this as a class variable if you want to keep debug prints
                print("Debug: Staff channel not available during cog load - this is normal during startup")
            return

        # Sort confessions by ID to maintain order
        sorted_confessions = sorted(
            self.confessions["active_confessions"].items(),
            key=lambda x: int(x[0])
        )

        # Clear existing message IDs
        self.confessions["message_ids"] = {}

        # Then create new messages with proper views
        for conf_id, conf_data in sorted_confessions:
            try:
                # Check if this is a temp ID (pending) or final ID
                display_id = "PENDING" if str(conf_id).startswith("temp_") else f"#{conf_id}"
                embed = discord.Embed(
                    title=f"Confession Form | Confession [ {display_id} ]",
                    description=conf_data["confession"],
                    color=self.EMBED_COLOR
                )
                embed.add_field(
                    name="Status:",
                    value=f"Awaiting Review {self.PENDING_EMOJI}",
                    inline=False
                )
                
                # Create view before sending message
                view = ConfessionButtons(
                    self.bot,
                    conf_data["user_id"],
                    conf_data["confession"],
                    conf_id
                )
                
                # Send message with view
                message = await staff_channel.send(embed=embed, view=view)
                
                # Update view with message ID and add to bot
                view.message_id = message.id
                self.bot.add_view(view)
                
                # Store the message ID
                self.confessions["message_ids"][conf_id] = message.id
                
            except Exception as e:
                print(f"Error restoring confession {conf_id}: {e}")
        
        # Save the updated confessions data
        await self.save_confessions()

    async def get_next_temp_id(self):
        """Get the next temporary ID for pending confessions"""
        if 'temp_id_counter' not in self.confessions:
            self.confessions['temp_id_counter'] = 0
        self.confessions['temp_id_counter'] += 1

        # Use a prefix to distinguish temp IDs from final IDs
        temp_id = f"temp_{self.confessions['temp_id_counter']}"
        print(f"🔍 Generated temporary confession ID: {temp_id}")

        return temp_id

    async def get_next_final_id(self):
        """Get the next sequential final ID for accepted confessions"""
        if 'last_id' not in self.confessions:
            self.confessions['last_id'] = 0
        self.confessions['last_id'] += 1

        # Debug print to verify ID generation
        print(f"🔍 Generated new final confession ID: {self.confessions['last_id']}")

        # Save just the ID for efficiency
        try:
            await db.update_confession_id(self.confessions['last_id'])
            print(f"✅ Saved confession ID {self.confessions['last_id']} to database")
        except Exception as e:
            print(f"❌ Error saving confession ID to database: {e}")

        return str(self.confessions['last_id'])

    async def save_confessions(self):
        """Save confessions data to database"""
        try:
            await db.save_confessions_data(self.confessions)
        except Exception as e:
            print(f"❌ Error saving confessions data: {e}")

    async def save_vote_data(self):
        """Save vote data to the database"""
        try:
            # Update the confessions structure with current vote data
            if "active_votes" not in self.confessions:
                self.confessions["active_votes"] = {}
            
            self.confessions["active_votes"] = self.active_votes
            
            # Save to database
            await self.save_confessions()
        except Exception as e:
            print(f"❌ Error saving vote data: {e}")

    def load_vote_data(self):
        """Load vote data from the confessions file"""
        if "active_votes" in self.confessions:
            for conf_id, vote_data in self.confessions["active_votes"].items():
                self.active_votes[conf_id] = {
                    "user_id": vote_data["user_id"],
                    "yes_votes": vote_data.get("yes_votes", []),
                    "no_votes": vote_data.get("no_votes", []),
                    "message_id": vote_data.get("message_id")
                }

    async def handle_confession_accept(self, interaction, user_id, confession_id):
        await interaction.response.defer()
        confession_id = str(confession_id)

        try:
            if confession_id not in self.confessions["active_confessions"]:
                await interaction.followup.send(
                    "This confession has already been processed.", 
                    ephemeral=True
                )
                return

            # Create disabled view for the message
            disabled_view = View()
            disabled_view.add_item(Button(
                label="︲Accept", 
                style=discord.ButtonStyle.gray, 
                emoji="<:Check:1349132518070157374>",
                disabled=True
            ))
            disabled_view.add_item(Button(
                label="︲Reject", 
                style=discord.ButtonStyle.gray, 
                emoji="<:XMark:1349132522600005756>",
                disabled=True
            ))
            disabled_view.add_item(Button(
                label="︲Vote", 
                style=discord.ButtonStyle.gray, 
                emoji="<:Vote:1352066177618415676>",
                disabled=True
            ))

            # Update original message
            embed = interaction.message.embeds[0]
            embed.clear_fields()
            embed.add_field(
                name="Status:",
                value=f"Accepted︲{self.ACCEPTED_EMOJI}",
                inline=False
            )
            await interaction.message.edit(embed=embed, view=disabled_view)

            # Get final ID for accepted confession
            final_id = await self.get_next_final_id()
            print(f"🔍 Assigned final ID {final_id} to accepted confession")

            # Send confession to confession channel
            confession_channel = self.bot.get_channel(self.CONFESSION_CHANNEL_ID)
            if confession_channel:
                embed = discord.Embed(
                    title=f"Anonymous Confession [ #{final_id} ]",
                    description=self.confessions["active_confessions"][confession_id]["confession"],
                    color=self.EMBED_COLOR
                )
                await confession_channel.send(embed=embed)

            # Send DM to user
            try:
                user = await self.bot.fetch_user(user_id)
                if user:
                    embed = discord.Embed(
                        description=f"{self.ACCEPTED_EMOJI}︲Your confession [ **#{final_id}** ] has been accepted!",
                        color=self.EMBED_COLOR
                    )
                    await user.send(embed=embed)
            except:
                pass

            # Clean up the confession data
            if "message_ids" in self.confessions and confession_id in self.confessions["message_ids"]:
                del self.confessions["message_ids"][confession_id]
            if confession_id in self.confessions["active_confessions"]:
                del self.confessions["active_confessions"][confession_id]
            await self.save_confessions()


        except Exception as e:
            await interaction.followup.send(f"An error occurred: {str(e)}", ephemeral=True)

    async def handle_confession_reject(self, interaction, user_id, confession_id):
        await interaction.response.defer()
        confession_id = str(confession_id)

        try:
            if confession_id not in self.confessions["active_confessions"]:
                await interaction.followup.send(
                    "This confession has already been processed.", 
                    ephemeral=True
                )
                return

            # Create disabled view for the message
            disabled_view = View()
            disabled_view.add_item(Button(
                label="︲Accept", 
                style=discord.ButtonStyle.gray, 
                emoji="<:Check:1349132518070157374>",
                disabled=True
            ))
            disabled_view.add_item(Button(
                label="︲Reject", 
                style=discord.ButtonStyle.gray, 
                emoji="<:XMark:1349132522600005756>",
                disabled=True
            ))
            disabled_view.add_item(Button(
                label="︲Vote", 
                style=discord.ButtonStyle.gray, 
                emoji="<:Vote:1352066177618415676>",
                disabled=True
            ))

            # Update original message
            embed = interaction.message.embeds[0]
            embed.clear_fields()
            embed.add_field(
                name="Status:",
                value=f"Rejected︲{self.REJECTED_EMOJI}",
                inline=False
            )
            await interaction.message.edit(embed=embed, view=disabled_view)

            # Send DM to user
            try:
                user = await self.bot.fetch_user(user_id)
                if user:
                    embed = discord.Embed(
                        description=f"{self.REJECTED_EMOJI}︲Your confession has been rejected.",
                        color=self.EMBED_COLOR
                    )
                    await user.send(embed=embed)
            except:
                pass

            # Clean up the confession data
            if "message_ids" in self.confessions and confession_id in self.confessions["message_ids"]:
                del self.confessions["message_ids"][confession_id]
            if confession_id in self.confessions["active_confessions"]:
                del self.confessions["active_confessions"][confession_id]
            await self.save_confessions()


        except Exception as e:
            await interaction.followup.send(f"An error occurred: {str(e)}", ephemeral=True)

    @app_commands.command(name="confess", description="Submit an anonymous confession")
    async def confess(self, interaction: discord.Interaction, confession: str):
        """Submit an anonymous confession"""
        try:
            # Check if we're in the right channel
            if interaction.channel.id != self.CONFESSION_CHANNEL_ID:
                embed = discord.Embed(
                    description="You can only use this command in the confessions channel!",
                    color=self.EMBED_COLOR
                )
                await interaction.response.send_message(embed=embed, ephemeral=True)
                return
            
            print(f"🔍 Channel check passed, proceeding with confession...")

            # Generate a temporary ID for pending confessions
            confession_id = await self.get_next_temp_id()
            print(f"🔍 Generated temporary confession ID: {confession_id}")

            # Send confirmation to user
            print(f"🔍 Sending confirmation to user...")
            embed = discord.Embed(
                description=f"Your confession [ **PENDING** ] has been sent and is awaiting approval!",
                color=self.EMBED_COLOR
            )
            await interaction.response.send_message(embed=embed, ephemeral=True)
            print(f"🔍 Confirmation sent to user successfully")

            # Store confession data
            print(f"🔍 Storing confession data...")
            print(f"🔍 Current confessions structure: {list(self.confessions.keys())}")
            
            if "active_confessions" not in self.confessions:
                self.confessions["active_confessions"] = {}
                print(f"🔍 Created active_confessions key")
                
            self.confessions["active_confessions"][confession_id] = {
                "user_id": interaction.user.id,
                "confession": confession,
                "submitted_at": datetime.utcnow().timestamp()
            }
            print(f"🔍 Confession data stored, saving to database...")
            await self.save_confessions()
            print(f"🔍 Confession data saved to database")

            # Send to staff channel for review
            try:
                print(f"🔍 Starting staff channel section...")
                print(f"🔍 Bot is ready: {self.bot.is_ready()}")
                print(f"🔍 Bot guilds count: {len(self.bot.guilds)}")
                print(f"🔍 Attempting to get staff channel with ID: {self.STAFF_CHANNEL_ID}")
                staff_channel = self.bot.get_channel(self.STAFF_CHANNEL_ID)
                print(f"🔍 Staff channel object: {staff_channel}")
                
                # If get_channel fails, try fetch_channel as a fallback
                if not staff_channel:
                    print(f"🔍 get_channel failed, trying fetch_channel...")
                    try:
                        staff_channel = await self.bot.fetch_channel(self.STAFF_CHANNEL_ID)
                        print(f"🔍 fetch_channel result: {staff_channel}")
                    except Exception as e:
                        print(f"🔍 fetch_channel also failed: {e}")
                
                if staff_channel:
                    print(f"🔍 Staff channel found: {staff_channel.name} (ID: {staff_channel.id})")
                    
                    # Check bot permissions
                    permissions = staff_channel.permissions_for(staff_channel.guild.me)
                    print(f"🔍 Bot permissions in staff channel:")
                    print(f"   - Send Messages: {permissions.send_messages}")
                    print(f"   - Embed Links: {permissions.embed_links}")
                    print(f"   - View Channel: {permissions.view_channel}")
                    
                    try:
                        embed = discord.Embed(
                            title=f"Confession Form | Confession [ PENDING ]",
                            description=confession,
                            color=self.EMBED_COLOR
                        )
                        embed.add_field(
                            name="Status:",
                            value=f"Awaiting Review︲{self.PENDING_EMOJI}",
                            inline=False
                        )

                        print(f"🔍 Creating ConfessionButtons view...")
                        view = ConfessionButtons(self.bot, interaction.user.id, confession, confession_id)
                        print(f"🔍 Attempting to send message to staff channel...")
                        message = await staff_channel.send(embed=embed, view=view)
                        print(f"🔍 Message sent successfully! Message ID: {message.id}")
                        
                        # Store the message ID for later reference
                        if "message_ids" not in self.confessions:
                            self.confessions["message_ids"] = {}
                        self.confessions["message_ids"][confession_id] = message.id
                        await self.save_confessions()
                        
                        print(f"✅ Confession #{confession_id} sent to staff channel successfully")
                    except Exception as e:
                        print(f"❌ Error sending confession to staff channel: {e}")
                        # Try to notify the user about the error
                        try:
                            error_embed = discord.Embed(
                                description=f"⚠️ Your confession was received but there was an error sending it to staff. Please contact an administrator. Error: {str(e)}",
                                color=0xFF0000
                            )
                            await interaction.followup.send(embed=error_embed, ephemeral=True)
                        except:
                            pass
                else:
                    print(f"❌ Staff channel not found! Channel ID: {self.STAFF_CHANNEL_ID}")
                    try:
                        error_embed = discord.Embed(
                            description="⚠️ Your confession was received but the staff channel is not available. Please contact an administrator.",
                            color=0xFF0000
                        )
                        await interaction.followup.send(embed=error_embed, ephemeral=True)
                    except:
                        pass
            except Exception as outer_e:
                print(f"❌ Outer exception in staff channel section: {outer_e}")
                import traceback
                traceback.print_exc()
        except Exception as e:
            print(f"❌ Error in confess command: {e}")
            await interaction.response.send_message("An error occurred while processing your confession. Please try again later.", ephemeral=True)

    async def vote_button_callback(self, interaction: discord.Interaction):
        """Handle vote button click"""
        title = interaction.message.embeds[0].title
        # Extract the ID from the title, handling both PENDING and numbered IDs
        id_part = title.split("[ ")[1].split(" ]")[0]
        if id_part == "PENDING":
            # Find the temp ID by matching the confession content
            confession_content = interaction.message.embeds[0].description
            confession_id = None
            for temp_id, conf_data in self.confessions["active_confessions"].items():
                if str(temp_id).startswith("temp_") and conf_data["confession"] == confession_content:
                    confession_id = temp_id
                    break
            if not confession_id:
                await interaction.response.send_message("Could not find the pending confession.", ephemeral=True)
                return
        else:
            confession_id = id_part.strip("#")
        
        if confession_id not in self.confessions["active_confessions"]:
            await interaction.response.send_message(
                f"This confession is no longer active. ID: {confession_id}", 
                ephemeral=True
            )
            return
            
        user_id = self.confessions["active_confessions"][confession_id]["user_id"]
        
        # Check if there's an existing vote view
        existing_votes = self.active_votes.get(confession_id, {})
        yes_votes = existing_votes.get("yes_votes", [])
        no_votes = existing_votes.get("no_votes", [])
        
        vote_view = VoteView(
            self.bot,
            user_id,
            confession_id,
            yes_votes=yes_votes,
            no_votes=no_votes
        )
        
        # Check if this is a temp ID (pending) or final ID
        display_id = "PENDING" if str(confession_id).startswith("temp_") else f"#{confession_id}"
        embed = discord.Embed(
            title=f"Vote To Reveal Username | Confession [ {display_id} ]",
            description="Needs a total of [ 3 ] \"Yes\" in order to see the username!",
            color=0x566374
        )
        
        await interaction.response.send_message(embed=embed, view=vote_view)
        message = await interaction.original_response()
        
        # Store the message reference in the view
        vote_view.message = message
        
        # Start the timeout task
        vote_view.timeout_task = asyncio.create_task(vote_view.start_timeout())
        
        # Store vote data
        self.active_votes[confession_id] = {
            "user_id": user_id,
            "yes_votes": list(vote_view.yes_votes),
            "no_votes": list(vote_view.no_votes),
            "message_id": message.id
        }
        await self.save_vote_data()
        
        # Add view to bot for persistence
        self.bot.add_view(vote_view)

async def setup(bot):
    cog = Confessions(bot)
    await bot.add_cog(cog)
    # Setup persistent views when the cog is loaded
    await cog.setup_persistent_views()






