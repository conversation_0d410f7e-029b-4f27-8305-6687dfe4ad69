# Responsible for handling the clear, ping, restart, and avatar commands

import discord
from discord import app_commands
from discord.ext import commands
import asyncio
import os
import aiohttp
import json
from .utils import admin_manager


class GeneralCommands(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.EMBED_COLOR = 0x566374  # Add this for consistent embed colors

        # Use centralized admin manager instead of local admin roles
        print(f"DEBUG: ===== GeneralCommands using AdminRoleManager =====")

    def has_admin_role(self, member: discord.Member) -> bool:
        """Check if member has admin role using centralized admin manager"""
        return admin_manager.has_admin_role(member)

    @app_commands.command(name="help", description="Shows all available commands and their descriptions")
    async def help(self, interaction: discord.Interaction):
        embed = discord.Embed(
            title="Bot Information",
            color=self.EMBED_COLOR
        )

        # General Commands
        general = """
`/avatar`︲Show a enlarged version of the selected user's profile picture
`/ping`︲Check the bot's response time and latency
`/confess`︲Submit an anonymous confession"""
        embed.add_field(name="General Commands [ Public Commands ]:", value=general, inline=False)

        # Counting Commands
        counting = """
`/countinghighscore`︲Display the highest counting streak achieved
`/countingleaderboard`︲Show the top users ranked by counting performance
`/countingstatistics`︲View your personal counting statistics and performance"""
        embed.add_field(name="Counting Commands [ Public Commands ]:", value=counting, inline=False)

        # Moderation Commands
        moderation = """
`/rolechannelsetup`︲Setup the role selection channel
`/voicecalltimeout`︲Restrict a user from joining voice calls for a set duration
`/voicecalluntimeout`︲Lift a user's voice call timeout restriction
`/warn`︲Issues a warning to a user
`/warnremove`︲Removes a specific warning from a member
`/userwarnings`︲Displays all warnings issued to a user
`/clearwarnings`︲Removes all warnings from a member"""
        embed.add_field(name="Moderation Commands [ Private Commands ]:", value=moderation, inline=False)

        # Credits
        credits = """
Developer: <@1322931389057994806> & **Augment AI Assistant**

Fir Emojis: https://twitter.com/ThatLazyRat"""
        embed.add_field(name="Credit:", value=credits, inline=False)

        await interaction.response.send_message(embed=embed)

    @app_commands.command(name="ping", description="Check the bot's response time and latency")
    async def ping(self, interaction: discord.Interaction):
        latency = round(self.bot.latency * 1000)
        await interaction.response.send_message(f"Pong! [ {latency}ms ]")

    @app_commands.command(name="restart", description="Redeploy the bot on Railway with current deployment")
    async def restart(self, interaction: discord.Interaction):
        if not self.has_admin_role(interaction.user):
            await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
            return

        await interaction.response.send_message("🔄 Redeploying bot on Railway... This will pull the latest code and restart the service.", ephemeral=True)

        print(f"Bot redeploy requested by {interaction.user.display_name}")

        try:
            # Give a moment for the message to send
            await asyncio.sleep(1)

            # Check if we have Railway environment variables
            railway_token = os.getenv('RAILWAY_TOKEN')
            environment_id = os.getenv('RAILWAY_ENVIRONMENT_ID')
            service_id = os.getenv('RAILWAY_SERVICE_ID')

            # Check if we're running on Railway
            is_railway_env = os.getenv('RAILWAY_ENVIRONMENT') is not None

            if not is_railway_env:
                print("Not running on Railway, falling back to simple restart...")
                await self.bot.close()
                return

            if not railway_token:
                print("RAILWAY_TOKEN not found, falling back to simple restart...")
                await self.bot.close()
                return

            if not environment_id or not service_id:
                print("RAILWAY_ENVIRONMENT_ID or RAILWAY_SERVICE_ID not found, falling back to simple restart...")
                await self.bot.close()
                return

            # Use Railway GraphQL API to trigger redeploy
            print("Triggering Railway redeploy via GraphQL API...")

            query = """
            mutation ServiceInstanceRedeploy {
                serviceInstanceRedeploy(
                    environmentId: "%s"
                    serviceId: "%s"
                )
            }
            """ % (environment_id, service_id)

            headers = {
                'Content-Type': 'application/json',
                'Authorization': f'Bearer {railway_token}'
            }

            payload = {
                'query': query
            }

            async with aiohttp.ClientSession() as session:
                async with session.post(
                    'https://backboard.railway.com/graphql/v2',
                    headers=headers,
                    json=payload
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        if 'errors' in result:
                            print(f"Railway API returned errors: {result['errors']}")
                            print("Falling back to simple bot restart...")
                            await self.bot.close()
                        else:
                            print("Railway redeploy triggered successfully via API")
                            print(f"API Response: {result}")
                            # Don't close the bot here - Railway will handle the redeploy
                    else:
                        print(f"Railway API request failed with status {response.status}")
                        response_text = await response.text()
                        print(f"Response: {response_text}")
                        print("Falling back to simple bot restart...")
                        await self.bot.close()

        except Exception as e:
            print(f"Error during redeploy: {e}")
            try:
                await interaction.followup.send(f"❌ Error during redeploy: {e}. Attempting simple restart...", ephemeral=True)
            except:
                pass
            # Fallback to simple restart
            print("Falling back to simple bot restart...")
            await self.bot.close()

    @app_commands.command(name="clear", description="Remove a designated number of messages from the channel")
    @app_commands.describe(amount="Number of messages to delete (1-100)")
    async def clear(self, interaction: discord.Interaction, amount: int):
        if not self.has_admin_role(interaction.user):
            await interaction.response.send_message("You don't have permission to use this command.", ephemeral=True)
            return

        if not 1 <= amount <= 100:
            await interaction.response.send_message(
                "Please specify a number between 1 and 100.", 
                ephemeral=True
            )
            return

        await interaction.response.defer(ephemeral=True)

        try:
            deleted = await interaction.channel.purge(limit=amount)
            await interaction.followup.send(
                f"Successfully deleted {len(deleted)} messages.", 
                ephemeral=True
            )
        except discord.Forbidden:
            await interaction.followup.send(
                "I don't have permission to delete messages in this channel.", 
                ephemeral=True
            )
        except discord.HTTPException as e:
            await interaction.followup.send(
                f"An error occurred while deleting messages: {str(e)}", 
                ephemeral=True
            )

    @app_commands.command(name="avatar", description="Show a enlarged version of the selected user's profile picture")
    @app_commands.describe(user="The user whose avatar you want to see")
    async def avatar(self, interaction: discord.Interaction, user: discord.Member):
        """Display a user's avatar"""
        embed = discord.Embed(
            title=f"Profile Picture [ {user.display_name} ]",
            color=0x566374
        )
        
        embed.set_image(url=user.display_avatar.url)
        
        await interaction.response.send_message(embed=embed)



async def setup(bot):
    await bot.add_cog(GeneralCommands(bot))

















