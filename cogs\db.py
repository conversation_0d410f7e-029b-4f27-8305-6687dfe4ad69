import asyncpg
import os
import json
from typing import Dict, Any, Optional, List
from datetime import datetime

class DatabaseManager:
    def __init__(self):
        self.pool = None
        self.connected = False
    
    async def connect(self):
        """Connect to PostgreSQL database using Railway connection details"""
        try:
            # Use the Railway DATABASE_URL
            database_url = "postgresql://postgres:<EMAIL>:5432/railway"
            
            # Create connection pool
            self.pool = await asyncpg.create_pool(
                database_url,
                min_size=1,
                max_size=10,
                command_timeout=60
            )
            
            # Test connection and create tables
            async with self.pool.acquire() as conn:
                await self._create_tables(conn)
            
            self.connected = True
            print("SUCCESS: Database connection established and tables created")
            
        except Exception as e:
            print(f"ERROR: Database connection failed: {e}")
            raise
    
    async def disconnect(self):
        """Close database connection pool"""
        if self.pool:
            await self.pool.close()
            self.connected = False
            print("SUCCESS: Database connection closed")
    
    async def _create_tables(self, conn):
        """Create all necessary tables for the bot data"""
        
        # Confessions table
        await conn.execute('''
            CREATE TABLE IF NOT EXISTS confessions (
                id SERIAL PRIMARY KEY,
                active_confessions JSONB DEFAULT '{}',
                last_id INTEGER DEFAULT 0,
                message_ids JSONB DEFAULT '{}',
                current_id INTEGER DEFAULT 0,
                active_votes JSONB DEFAULT '{}',
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Counting table
        await conn.execute('''
            CREATE TABLE IF NOT EXISTS counting (
                id SERIAL PRIMARY KEY,
                current_number INTEGER DEFAULT 0,
                highest_number INTEGER DEFAULT 0,
                last_counter TEXT,
                counter_stats JSONB DEFAULT '{}',
                failure_stats JSONB DEFAULT '{}',
                current_streak INTEGER DEFAULT 0,
                current_streak_user TEXT,
                highest_streaks JSONB DEFAULT '{}',
                saves JSONB DEFAULT '{}',
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Role messages table
        await conn.execute('''
            CREATE TABLE IF NOT EXISTS role_messages (
                id SERIAL PRIMARY KEY,
                data JSONB DEFAULT '{}',
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # User forms table
        await conn.execute('''
            CREATE TABLE IF NOT EXISTS user_forms (
                id SERIAL PRIMARY KEY,
                data JSONB DEFAULT '{}',
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # VC timeouts table
        await conn.execute('''
            CREATE TABLE IF NOT EXISTS vc_timeouts (
                id SERIAL PRIMARY KEY,
                data JSONB DEFAULT '{}',
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Warnings table
        await conn.execute('''
            CREATE TABLE IF NOT EXISTS warnings (
                id SERIAL PRIMARY KEY,
                warnings JSONB DEFAULT '{}',
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Initialize tables with default data if they're empty
        await self._initialize_default_data(conn)
    
    async def _initialize_default_data(self, conn):
        """Initialize tables with default data if they're empty"""
        
        # Initialize confessions
        count = await conn.fetchval('SELECT COUNT(*) FROM confessions')
        if count == 0:
            # Get the highest confession ID from the database if it exists
            highest_id = await conn.fetchval('SELECT MAX(last_id) FROM confessions')
            highest_id = highest_id if highest_id is not None else 0
            
            await conn.execute('''
                INSERT INTO confessions (active_confessions, last_id, message_ids, current_id, active_votes)
                VALUES ('{}', $1, '{}', $1, '{}')
            ''', highest_id)
        
        # Initialize counting
        count = await conn.fetchval('SELECT COUNT(*) FROM counting')
        if count == 0:
            await conn.execute('''
                INSERT INTO counting (current_number, highest_number, last_counter, counter_stats, 
                                    failure_stats, current_streak, current_streak_user, highest_streaks, saves)
                VALUES (0, 0, NULL, '{}', '{}', 0, NULL, '{}', '{}')
            ''')
        
        # Initialize role_messages
        count = await conn.fetchval('SELECT COUNT(*) FROM role_messages')
        if count == 0:
            await conn.execute('INSERT INTO role_messages (data) VALUES (\'{}\')')
        
        # Initialize user_forms
        count = await conn.fetchval('SELECT COUNT(*) FROM user_forms')
        if count == 0:
            await conn.execute('INSERT INTO user_forms (data) VALUES (\'{}\')')
        
        # Initialize vc_timeouts
        count = await conn.fetchval('SELECT COUNT(*) FROM vc_timeouts')
        if count == 0:
            await conn.execute('INSERT INTO vc_timeouts (data) VALUES (\'{}\')')
        
        # Initialize warnings
        count = await conn.fetchval('SELECT COUNT(*) FROM warnings')
        if count == 0:
            await conn.execute('INSERT INTO warnings (warnings) VALUES (\'{}\')')
    
    # Confessions methods
    async def get_confessions_data(self) -> Dict[str, Any]:
        """Get confessions data"""
        async with self.pool.acquire() as conn:
            # First, ensure the table exists and has at least one row
            count = await conn.fetchval('SELECT COUNT(*) FROM confessions')
            if count == 0:
                # Initialize with default data
                await self._initialize_default_data(conn)
            
            row = await conn.fetchrow('SELECT * FROM confessions ORDER BY id LIMIT 1')
            if row:
                # Handle case where JSONB data might be returned as strings
                def parse_json_field(field_data):
                    if isinstance(field_data, str):
                        import json
                        try:
                            return json.loads(field_data)
                        except json.JSONDecodeError:
                            return {}
                    return field_data if isinstance(field_data, dict) else {}
                
                # Print the actual values from database for debugging
                print(f"DEBUG: Database last_id = {row['last_id']}, current_id = {row['current_id']}")
                
                return {
                    'active_confessions': parse_json_field(row['active_confessions']),
                    'last_id': row['last_id'],
                    'message_ids': parse_json_field(row['message_ids']),
                    'current_id': row['current_id'],
                    'active_votes': parse_json_field(row['active_votes'])
                }
            return {
                'active_confessions': {},
                'last_id': 0,
                'message_ids': {},
                'current_id': 0,
                'active_votes': {}
            }
    
    async def save_confessions_data(self, data: Dict[str, Any]):
        """Save confessions data"""
        async with self.pool.acquire() as conn:
            # Convert dictionary fields to JSON strings for PostgreSQL
            active_confessions = json.dumps(data.get('active_confessions', {}))
            message_ids = json.dumps(data.get('message_ids', {}))
            active_votes = json.dumps(data.get('active_votes', {}))
            
            await conn.execute('''
                UPDATE confessions SET 
                    active_confessions = $1::jsonb,
                    last_id = $2,
                    message_ids = $3::jsonb,
                    current_id = $4,
                    active_votes = $5::jsonb,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = 1
            ''', 
            active_confessions,
            data.get('last_id', 0),
            message_ids,
            data.get('current_id', 0),
            active_votes
            )
    
    async def update_confession_id(self, last_id: int):
        """Update just the last_id in the database for efficiency"""
        async with self.pool.acquire() as conn:
            await conn.execute('''
                UPDATE confessions SET 
                    last_id = $1,
                    current_id = $1,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = 1
            ''', last_id)
            
            # If no rows were updated, insert a new row
            if await conn.fetchval('SELECT COUNT(*) FROM confessions') == 0:
                await conn.execute('''
                    INSERT INTO confessions (active_confessions, last_id, message_ids, current_id, active_votes)
                    VALUES ('{}', $1, '{}', $1, '{}')
                ''', last_id)
    
    # Counting methods
    async def get_counting_data(self, guild_id: int = None) -> Dict[str, Any]:
        """Get counting data"""
        async with self.pool.acquire() as conn:
            row = await conn.fetchrow('SELECT * FROM counting ORDER BY id LIMIT 1')
            if row:
                try:
                    # Safely convert JSON fields to dicts, handling potential format issues
                    counter_stats = row['counter_stats']
                    if isinstance(counter_stats, dict):
                        counter_stats = counter_stats
                    elif isinstance(counter_stats, str):
                        counter_stats = json.loads(counter_stats)
                    else:
                        counter_stats = {}
                    
                    failure_stats = row['failure_stats']
                    if isinstance(failure_stats, dict):
                        failure_stats = failure_stats
                    elif isinstance(failure_stats, str):
                        failure_stats = json.loads(failure_stats)
                    else:
                        failure_stats = {}
                    
                    highest_streaks = row['highest_streaks']
                    if isinstance(highest_streaks, dict):
                        highest_streaks = highest_streaks
                    elif isinstance(highest_streaks, str):
                        highest_streaks = json.loads(highest_streaks)
                    else:
                        highest_streaks = {}
                    
                    saves = row['saves']
                    if isinstance(saves, dict):
                        saves = saves
                    elif isinstance(saves, str):
                        saves = json.loads(saves)
                    else:
                        saves = {}
                    
                    return {
                        'current_number': row['current_number'],
                        'highest_number': row['highest_number'],
                        'last_counter': row['last_counter'],
                        'counter_stats': counter_stats,
                        'failure_stats': failure_stats,
                        'current_streak': row['current_streak'],
                        'current_streak_user': row['current_streak_user'],
                        'highest_streaks': highest_streaks,
                        'saves': saves
                    }
                except Exception as e:
                    print(f"Error parsing counting data: {e}")
                    # Return defaults if parsing fails
                    pass
            
            return {
                'current_number': 0,
                'highest_number': 0,
                'last_counter': None,
                'counter_stats': {},
                'failure_stats': {},
                'current_streak': 0,
                'current_streak_user': None,
                'highest_streaks': {},
                'saves': {}
            }
    
    async def save_counting_data(self, data: Dict[str, Any]):
        """Save counting data"""
        async with self.pool.acquire() as conn:
            await conn.execute('''
                UPDATE counting SET 
                    current_number = $1,
                    highest_number = $2,
                    last_counter = $3,
                    counter_stats = $4,
                    failure_stats = $5,
                    current_streak = $6,
                    current_streak_user = $7,
                    highest_streaks = $8,
                    saves = $9,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = 1
            ''',
            data.get('current_number', 0),
            data.get('highest_number', 0),
            data.get('last_counter'),
            data.get('counter_stats', {}),
            data.get('failure_stats', {}),
            data.get('current_streak', 0),
            data.get('current_streak_user'),
            data.get('highest_streaks', {}),
            data.get('saves', {})
            )
    
    async def update_counting_data(self, guild_id: int, **kwargs):
        """Update counting data with specific fields"""
        async with self.pool.acquire() as conn:
            # Get current data first
            current_data = await self.get_counting_data(guild_id)
            
            # Update with provided kwargs
            for key, value in kwargs.items():
                if key in current_data:
                    current_data[key] = value
            
            # Save updated data
            await self.save_counting_data(current_data)
    
    async def get_user_counting_stats(self, user_id: int) -> Dict[str, Any]:
        """Get user-specific counting statistics"""
        async with self.pool.acquire() as conn:
            # Create user_counting_stats table if it doesn't exist
            await conn.execute('''
                CREATE TABLE IF NOT EXISTS user_counting_stats (
                    user_id BIGINT PRIMARY KEY,
                    correct_counts INTEGER DEFAULT 0,
                    failed_counts INTEGER DEFAULT 0,
                    saves INTEGER DEFAULT 0,
                    highest_streak INTEGER DEFAULT 0,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            row = await conn.fetchrow('SELECT * FROM user_counting_stats WHERE user_id = $1', user_id)
            if row:
                return {
                    'correct_counts': row['correct_counts'],
                    'failed_counts': row['failed_counts'],
                    'saves': row['saves'],
                    'highest_streak': row['highest_streak']
                }
            return {
                'correct_counts': 0,
                'failed_counts': 0,
                'saves': 0,
                'highest_streak': 0
            }
    
    async def update_user_counting_stats(self, user_id: int, **kwargs):
        """Update user-specific counting statistics"""
        async with self.pool.acquire() as conn:
            # Ensure table exists
            await conn.execute('''
                CREATE TABLE IF NOT EXISTS user_counting_stats (
                    user_id BIGINT PRIMARY KEY,
                    correct_counts INTEGER DEFAULT 0,
                    failed_counts INTEGER DEFAULT 0,
                    saves INTEGER DEFAULT 0,
                    highest_streak INTEGER DEFAULT 0,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Insert or update user stats
            await conn.execute('''
                INSERT INTO user_counting_stats (user_id, correct_counts, failed_counts, saves, highest_streak)
                VALUES ($1, $2, $3, $4, $5)
                ON CONFLICT (user_id) DO UPDATE SET
                    correct_counts = COALESCE($2, user_counting_stats.correct_counts),
                    failed_counts = COALESCE($3, user_counting_stats.failed_counts),
                    saves = COALESCE($4, user_counting_stats.saves),
                    highest_streak = COALESCE($5, user_counting_stats.highest_streak),
                    updated_at = CURRENT_TIMESTAMP
            ''',
            user_id,
            kwargs.get('correct_counts'),
            kwargs.get('failed_counts'),
            kwargs.get('saves'),
            kwargs.get('highest_streak')
            )
    
    # Role messages methods
    async def get_role_messages_data(self) -> Dict[str, Any]:
        """Get role messages data"""
        async with self.pool.acquire() as conn:
            row = await conn.fetchrow('SELECT data FROM role_messages ORDER BY id LIMIT 1')
            if row and row['data']:
                data = row['data']
                # Handle case where data might be a string
                if isinstance(data, str):
                    import json
                    try:
                        return json.loads(data)
                    except json.JSONDecodeError:
                        return {}
                return data if isinstance(data, dict) else {}
            return {}
    
    async def save_role_messages_data(self, data: Dict[str, Any]):
        """Save role messages data"""
        async with self.pool.acquire() as conn:
            await conn.execute('''
                UPDATE role_messages SET 
                    data = $1::jsonb,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = 1
            ''', json.dumps(data))

    async def get_all_role_messages(self) -> Dict[str, Any]:
        """Get all role messages data (alias for get_role_messages_data)"""
        return await self.get_role_messages_data()
    
    async def save_role_message(self, message_id, channel_id, role_data):
        """Save individual role message data to database"""
        # Get current data
        current_data = await self.get_role_messages_data()
        
        # Ensure proper structure: channel_id -> message_id -> category_name
        channel_id_str = str(channel_id)
        message_id_str = str(message_id)
        
        if channel_id_str not in current_data:
            current_data[channel_id_str] = {}
        
        # role_data should contain {message_id: category_name}
        if message_id_str in role_data:
            current_data[channel_id_str][message_id_str] = role_data[message_id_str]
        
        # Save updated data back to database
        await self.save_role_messages_data(current_data)
    
    async def clear_all_role_messages(self):
        """Clear all role messages from database"""
        await self.save_role_messages_data({})
    
    # User forms methods
    async def get_user_forms_data(self) -> Dict[str, Any]:
        """Get user forms data"""
        async with self.pool.acquire() as conn:
            row = await conn.fetchrow('SELECT data FROM user_forms ORDER BY id LIMIT 1')
            if row and row['data']:
                data = row['data']
                # Handle case where data might be a string
                if isinstance(data, str):
                    import json
                    try:
                        return json.loads(data)
                    except json.JSONDecodeError:
                        return {}
                return data if isinstance(data, dict) else {}
            return {}
    
    async def save_user_forms_data(self, data: Dict[str, Any]):
        """Save user forms data"""
        async with self.pool.acquire() as conn:
            await conn.execute('''
                UPDATE user_forms SET 
                    data = $1,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = 1
            ''', data)

    async def get_user_form(self, form_id: str) -> Dict[str, Any]:
        """Get a specific user form by ID"""
        data = await self.get_user_forms_data()
        return data.get(form_id, {})
    
    # VC timeouts methods
    async def get_vc_timeouts_data(self) -> Dict[str, Any]:
        """Get VC timeouts data"""
        async with self.pool.acquire() as conn:
            row = await conn.fetchrow('SELECT data FROM vc_timeouts ORDER BY id LIMIT 1')
            if row and row['data']:
                data = row['data']
                # Handle case where data might be a string
                if isinstance(data, str):
                    import json
                    try:
                        return json.loads(data)
                    except json.JSONDecodeError:
                        return {}
                return data if isinstance(data, dict) else {}
            return {}
    
    async def save_vc_timeouts_data(self, data: Dict[str, Any]):
        """Save VC timeouts data"""
        async with self.pool.acquire() as conn:
            # Convert dictionary to JSON string for PostgreSQL
            import json
            json_data = json.dumps(data)

            await conn.execute('''
                INSERT INTO vc_timeouts (id, data, updated_at)
                VALUES (1, $1::jsonb, CURRENT_TIMESTAMP)
                ON CONFLICT (id) DO UPDATE SET
                    data = EXCLUDED.data,
                    updated_at = EXCLUDED.updated_at
            ''', json_data)
    
    async def get_vc_timeout(self, user_id: int) -> float:
        """Get VC timeout for a specific user"""
        try:
            data = await self.get_vc_timeouts_data()
            return data.get(str(user_id))
        except Exception as e:
            print(f"Error getting VC timeout for user {user_id}: {e}")
            return None
    
    async def save_vc_timeout(self, user_id: int, timeout_timestamp: float):
        """Save VC timeout for a specific user"""
        try:
            data = await self.get_vc_timeouts_data()
            # Ensure data is a dictionary
            if not isinstance(data, dict):
                data = {}
            data[str(user_id)] = timeout_timestamp
            await self.save_vc_timeouts_data(data)
            print(f"Successfully saved VC timeout for user {user_id}")
        except Exception as e:
            print(f"Error saving VC timeout for user {user_id}: {e}")
            raise
    
    async def remove_vc_timeout(self, user_id: int):
        """Remove VC timeout for a specific user"""
        try:
            data = await self.get_vc_timeouts_data()
            if isinstance(data, dict) and str(user_id) in data:
                del data[str(user_id)]
                await self.save_vc_timeouts_data(data)
                print(f"Successfully removed VC timeout for user {user_id}")
        except Exception as e:
            print(f"Error removing VC timeout for user {user_id}: {e}")
            raise
    
    # Warnings methods
    async def get_warnings_data(self) -> Dict[str, Any]:
        """Get warnings data with robust error handling"""
        async with self.pool.acquire() as conn:
            try:
                row = await conn.fetchrow('SELECT warnings FROM warnings ORDER BY id LIMIT 1')
                if row and row['warnings'] is not None:
                    warnings_data = row['warnings']
                    # Handle different data types that might come from the database
                    if isinstance(warnings_data, dict):
                        # Already a dictionary (JSONB parsed by asyncpg)
                        return {'warnings': warnings_data}
                    elif isinstance(warnings_data, str):
                        # String that needs JSON parsing
                        try:
                            parsed_data = json.loads(warnings_data)
                            return {'warnings': parsed_data if isinstance(parsed_data, dict) else {}}
                        except (json.JSONDecodeError, TypeError):
                            print(f"Failed to parse warnings JSON: {warnings_data}")
                            return {'warnings': {}}
                    else:
                        print(f"Unexpected warnings data type: {type(warnings_data)}")
                        return {'warnings': {}}
                else:
                    # No data found, return empty structure
                    return {'warnings': {}}
            except Exception as e:
                print(f"Error getting warnings data: {e}")
                return {'warnings': {}}
    
    async def save_warnings_data(self, data: Dict[str, Any]):
        """Save warnings data with robust error handling"""
        async with self.pool.acquire() as conn:
            try:
                warnings_dict = data.get('warnings', {})
                # Ensure we're saving a proper dictionary
                if not isinstance(warnings_dict, dict):
                    warnings_dict = {}
                
                # Convert to JSON string for JSONB storage
                warnings_json = json.dumps(warnings_dict)
                print(f"Saving warnings data: {warnings_json[:100]}...")
                
                await conn.execute('''
                    UPDATE warnings SET 
                        warnings = $1::jsonb,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = 1
                ''', warnings_json)
                
                # If no rows were updated, insert a new one
                result = await conn.fetchval('SELECT COUNT(*) FROM warnings WHERE id = 1')
                if result == 0:
                    await conn.execute('''
                        INSERT INTO warnings (id, warnings, updated_at) 
                        VALUES (1, $1::jsonb, CURRENT_TIMESTAMP)
                    ''', warnings_json)
                    
            except Exception as e:
                print(f"Error saving warnings data: {e}")
                raise
    
    async def add_warning(self, user_id: int, reason: str, warned_by: int, warned_by_name: str):
        """Add a warning to a user with robust error handling"""
        try:
            # Get current warnings with error handling
            warnings_data = await self.get_warnings_data()
            warnings = warnings_data.get('warnings', {})
            
            # Ensure warnings is a dictionary
            if not isinstance(warnings, dict):
                warnings = {}
            
            # Initialize user warnings if not exists
            user_id_str = str(user_id)
            if user_id_str not in warnings:
                warnings[user_id_str] = []
            
            # Ensure user's warnings is a list
            if not isinstance(warnings[user_id_str], list):
                warnings[user_id_str] = []
            
            # Add new warning
            warning = {
                'reason': str(reason),
                'warned_by': int(warned_by),
                'warned_by_name': str(warned_by_name),
                'timestamp': datetime.now().isoformat()
            }
            warnings[user_id_str].append(warning)
            
            # Save updated warnings
            await self.save_warnings_data({'warnings': warnings})
            print(f"SUCCESS: Warning added for user {user_id}: {reason}")
            
        except Exception as e:
            print(f"ERROR: Error adding warning for user {user_id}: {e}")
            raise
    
    async def get_user_warnings(self, user_id: int):
        """Get all warnings for a specific user with error handling"""
        try:
            warnings_data = await self.get_warnings_data()
            warnings = warnings_data.get('warnings', {})
            
            # Ensure warnings is a dictionary
            if not isinstance(warnings, dict):
                return []
            
            user_warnings = warnings.get(str(user_id), [])
            
            # Ensure user warnings is a list
            if not isinstance(user_warnings, list):
                return []
            
            return user_warnings
            
        except Exception as e:
            print(f"ERROR: Error getting warnings for user {user_id}: {e}")
            return []
    
    async def remove_warning(self, user_id: int, warning_index: int):
        """Remove a specific warning from a user with error handling"""
        try:
            warnings_data = await self.get_warnings_data()
            warnings = warnings_data.get('warnings', {})
            
            # Ensure warnings is a dictionary
            if not isinstance(warnings, dict):
                return False
            
            user_id_str = str(user_id)
            if user_id_str not in warnings:
                return False
            
            # Ensure user warnings is a list
            if not isinstance(warnings[user_id_str], list):
                return False
            
            if 0 <= warning_index < len(warnings[user_id_str]):
                warnings[user_id_str].pop(warning_index)
                
                # Remove user entry if no warnings left
                if not warnings[user_id_str]:
                    del warnings[user_id_str]
                
                # Save updated warnings
                await self.save_warnings_data({'warnings': warnings})
                print(f"SUCCESS: Warning {warning_index + 1} removed for user {user_id}")
                return True
            
            return False
            
        except Exception as e:
            print(f"ERROR: Error removing warning for user {user_id}: {e}")
            return False
    
    # Additional helper methods for confessions
    async def get_active_confessions(self) -> List[Dict[str, Any]]:
        """Get list of active confessions"""
        data = await self.get_confessions_data()
        active_confessions = data.get('active_confessions', {})
        
        # Convert to list format expected by the cog
        result = []
        for conf_id, confession in active_confessions.items():
            result.append({
                'id': conf_id,
                'user_id': confession.get('user_id'),
                'content': confession.get('content'),
                'staff_message_id': confession.get('staff_message_id')
            })
        return result

# Global database instance
db = DatabaseManager()
